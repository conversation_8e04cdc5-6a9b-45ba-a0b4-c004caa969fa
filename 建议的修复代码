nlohmann::json canvas_item_filter_json = nlohmann::json::array();  // 改为数组
nlohmann::json canvas_filter_json = nlohmann::json::array();       // 改为数组

for (int i = 0; i < vibeTriggerEffect.filterInfos.size(); ++i)
{
    const VIBE_FILTER_INFO& filter_info = vibeTriggerEffect.filterInfos[i];
    const FILTER&           filter = filter_info.filter;

    std::string media_id = "";
    Util::NumToString(filter.mediaIDs[0], &media_id);
    std::string filter_id = "";
    Util::NumToString(filter.id, &filter_id);

    if (filter.type == FILTER_VISUAL || filter.type == FILTER_EFFECT)
    {
        nlohmann::json item_filter_json;  // 为每个filter创建新对象
        item_filter_json["action_id"] = filter_info.actionID;
        item_filter_json["canvas_item_id"] = media_id;
        item_filter_json["filter_id"] = filter_id;
        
        if (filter_info.actionType == VIBE_ACTION_TYPE_CREATE)
        {
            item_filter_json["action_type"] = "create";
            // ... 其他create逻辑保持不变
        }
        else if (filter_info.actionType == VIBE_ACTION_TYPE_UPDATE)
        {
            // ... update逻辑保持不变
        }
        else if (filter_info.actionType == VIBE_ACTION_TYPE_DESTROY)
        {
            item_filter_json["action_type"] = "destroy";
        }
        else if (filter_info.actionType == VIBE_ACTION_TYPE_ACTIVE)
        {
            item_filter_json["action_type"] = "set_active";
            item_filter_json["active"] = filter.enable.has_value() ? filter.enable.value() : false;
        }
        
        canvas_item_filter_json.push_back(item_filter_json);  // 添加到数组
    }
    else if (filter.type == FILTER_CANVAS)
    {
        nlohmann::json canvas_filter_item_json;  // 为每个filter创建新对象
        canvas_filter_item_json["action_id"] = filter_info.actionID;
        canvas_filter_item_json["canvas_id"] = media_id;  // 修正字段名
        canvas_filter_item_json["filter_id"] = filter_id;
        
        if (filter_info.actionType == VIBE_ACTION_TYPE_CREATE)
        {
            canvas_filter_item_json["action_type"] = "create";
            // ... create逻辑保持不变
        }
        else if (filter_info.actionType == VIBE_ACTION_TYPE_DESTROY)
        {
            canvas_filter_item_json["action_type"] = "destroy";
        }
        else if (filter_info.actionType == VIBE_ACTION_TYPE_ACTIVE)
        {
            canvas_filter_item_json["action_type"] = "set_active";
            canvas_filter_item_json["active"] = filter.enable.has_value() ? filter.enable.value() : false;
        }
        
        canvas_filter_json.push_back(canvas_filter_item_json);  // 添加到数组
    }
}

ambient_json["canvas_item_filter"] = canvas_item_filter_json;
ambient_json["canvas_filter"] = canvas_filter_json;