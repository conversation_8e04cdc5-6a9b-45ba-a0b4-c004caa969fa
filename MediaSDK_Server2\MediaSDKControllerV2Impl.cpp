#include "MediaSDKControllerV2Impl.h"

#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <mutex>
#include <thread>
#include <atomic>
#include <functional>
#include <future>
#include <condition_variable>
#include <chrono>
#include <sstream>
#include <fstream>
#include <variant>

#include "DLLLoader.h"
#include "LSExport.h"
#include "ciefhelper.h"
#include "LSPublicHeader.h"
#include "stringutil.h"
#include "Common/TinyLogging_win32.h"
#include "mediasdk_v2_header/mediasdk_defines_rtc.h"
#include "mediasdk_v2_header/mediasdk_callback_defines.h"
#include "mediasdk_v2_header/mediasdk_defines_stream_error_code.h"
#include "mediasdk_v2_header/mediasdk_array.hpp"
#include "mediasdk_v2_header/base/base64.hpp"
#include "mediasdk_v2_header/plugins/browser_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/image_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/desktop_capture_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/fav_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/wasapi_audio_source_helper.h"
#include "mediasdk_v2_header/plugins/app_audio_capture_helper.h"
#include "mediasdk_v2_header/plugins/dshow_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/bytelink_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/graffiti_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/game_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/rtc_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/effect_visual_filter_helper.h"
#include "mediasdk_v2_header/hook_api/hook_api.h"
#include "NativeBridge/StreamSEIBuilder.h"
#include "librtc/rtc/bytertc_rts_defines.h"
#include "ModeSceneMgr.h"

extern CIEF::ICIEF* g_cief;
int                 g_video_fps = 1;
const static int32_t kV2CallTimeoutMS = 3000;

sdk_helper::MediaSDKControllerImplV2* g_sdkController = nullptr;

namespace sdk_helper
{

std::atomic_uint64_t CallHelper::g_call_id_ = 0;
static std::mutex* g_init_mutex = new std::mutex;
MediaSDKV2API g_sdk_api;

class ScopeCallTimeCal
{
public:
	ScopeCallTimeCal(bool logNow, std::string funcName)
		: logNow_(logNow), funcName_(funcName)
	{
		start_time_ = TinyTime::Now();
		call_id_ = ++s_call_id_;
		if (logNow_)
		{
			LOG(INFO) << "[call id : " << call_id_ << "] " << funcName_ << " [begin]";
		}
	}

	~ScopeCallTimeCal()
	{
		INT64 end_time = TinyTime::Now();
		if (logNow_)
		{
			LOG(INFO) << "[call id: " << call_id_ << "] " << funcName_ << " [end], elapsed time: " << (end_time - start_time_) / 1000000 << " ms.";
		}
	}

private:
	INT64                       start_time_;
	bool                        logNow_;
	std::string                 funcName_;
	static std::atomic_uint64_t s_call_id_;
	uint64_t                    call_id_;
};

class NativeTask final : public CIEF::ITask
{
public:
    explicit NativeTask(const closure& task)
    {
        m_task = task;
    }

    void Release() override
    {
        delete this;
    }

    void DoTask(CIEFGUID thread) override
    {
        m_task();
    }

    void QuitTask() override
    {
    }

protected:
    closure m_task;
};

std::atomic_uint64_t ScopeCallTimeCal::s_call_id_ = 0;
std::map<std::string, STREAM_TYPE> g_startStreamType;
std::mutex                         g_mutex_;
std::set<std::string>              g_rtc_visual_ids;

#pragma pack(push)
#pragma pack(4)

struct MonoAudioPCMS16DataHeader
{
    char    audio_input_id[64];
    int32_t audio_input_id_len;
    int64_t timestamp_ns;
    int32_t nb_sample_count;
    int32_t data_len;
};

#pragma pack(pop)

static const int AUDIO_TRACK_MAX = 6; // set max track id is 6

static bool GetFirstAudioTrackOfTrackIds(uint32_t& track_ids, uint32_t& first_track)
{
    int  flag = 1;
    bool has_track = false;
    for (int i = 0; i < AUDIO_TRACK_MAX; i++)
    {
        if (track_ids & (flag << i))
        {
            first_track = i + 1;
            track_ids = track_ids & (~(flag << i));
            has_track = true;
            break;
        }
    }
    return has_track;
}

static mediasdk::MSTransform GetMediaSdkTransFrom(const TRANSFORM& transformIn)
{
    mediasdk::MSTransform transform;

    transform.flip_h = transformIn.hFlip;
    transform.flip_v = transformIn.vFlip;
    transform.angle = transformIn.angle;
    transform.scale = { transformIn.scale.X, transformIn.scale.Y };
    transform.clip = { transformIn.clipRange.x, transformIn.clipRange.y, transformIn.clipRange.z, transformIn.clipRange.w };
    transform.translate = {transformIn.translate.X, transformIn.translate.Y};

    return transform;
}

void* __stdcall MediaSDKControllerV2Impl_AllocBuffer(uint32_t size)
{
    return g_sdk_api.AllocBuffer(size);
}

void __stdcall MediaSDKControllerV2Impl_FreeBuffer(void* p)
{
    return g_sdk_api.FreeBuffer(p);
}

#define ASSIGN_API_FUNCTION_OR_BREAK(api, functionName, procName, dll)                                          \
    if (!(api->functionName = (decltype(api->functionName))GetProcAddress(dll, #procName)))                     \
    {                                                                                                           \
        LOG(ERROR) << "find func error: " << #functionName << " not found in mediasdk.dll please check again."; \
        break;                                                                                                  \
    }

static bool MediaSDKV2LoadSymbols(MediaSDKV2API* api)
{
    bool success = false;

    do
    {
        std::wstring              folder = dll_helper::GetCurrentModuleDirectory() + L"\\MediaSDK_V2";
        std::vector<std::wstring> folders{
            folder,
            folder + L"\\wired",
            folder + L"\\plugins",
            folder + L"\\libeffect",
            folder + L"\\librtc"};
        HMODULE dll = dll_helper::LoadLibraryFromFolder(L"mediasdk.dll", folders, {});
        if (!dll)
        {
            std::string ansi_folder;
            dll_helper::ConvertWideCharToAnsi(folder, &ansi_folder);
            LOG(ERROR) << "[mediasdk.dll] Load failed, Load path: " << ansi_folder;
            break;
        }

        // Origin Transition
        // video_model
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateModel, CreateModel, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVideoModelActive, SetVideoModelActive, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetVideoModelActive, GetVideoModelActive, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, IsPreviewEnableWithVideoModelId, IsPreviewEnableWithVideoModelId, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnablePreviewWithVideoModelId, EnablePreviewWithVideoModelId, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetScaleForVideoModel, SetScaleForVideoModel, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemByOrderIDSOnVideoModel, SetCanvasItemByOrderIDSOnVideoModel, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemOrderIDSOnVideoModel, GetCanvasItemOrderIDSOnVideoModel, dll);

        // canvas
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateCanvas, CreateCanvas, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, DestroyCanvas, DestroyCanvas, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCurrentCanvas, GetCurrentCanvas, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCurrentCanvas, SetCurrentCanvas, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemByOrderIDS, SetCanvasItemByOrderIDS, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemOrderIDS, GetCanvasItemOrderIDS, dll);

        // canvas_item
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateCanvasItem, CreateCanvasItem, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, DestroyCanvasItem, DestroyCanvasItem, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateCanvasItemWithFilter, CreateCanvasItemWithFilter, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, CanvasItemChangeVisual, CanvasItemChangeVisual, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemIdsFromVisual, GetCanvasItemIdsFromVisual, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCurrentCanvasItem, SetCurrentCanvasItem, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCurrentCanvasItem, GetCurrentCanvasItem, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCurrentCanvasItemOnVideoModel, SetCurrentCanvasItemOnVideoModel, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCurrentCanvasItemOnVideoModel, GetCurrentCanvasItemOnVideoModel, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetVisualFromCanvasItem, GetVisualFromCanvasItem, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemNeedDrawBorder, SetCanvasItemNeedDrawBorder, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemAlwaysTop, SetCanvasItemAlwaysTop, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemAvoidOutput, SetCanvasItemAvoidOutput, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, BeginCanvasItemClip, BeginCanvasItemClip, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemFlipH, SetCanvasItemFlipH, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemFlipH, GetCanvasItemFlipH, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemFlipV, SetCanvasItemFlipV, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemFlipV, GetCanvasItemFlipV, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemMoveRange, SetCanvasItemMoveRange, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemMoveRange, GetCanvasItemMoveRange, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemRotate, SetCanvasItemRotate, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemRotate, GetCanvasItemRotate, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemHighlight, SetCanvasItemHighlight, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemScale, SetCanvasItemScale, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemScale, GetCanvasItemScale, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemMinScale, SetCanvasItemMinScale, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemMinScale, GetCanvasItemMinScale, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemMaxScale, SetCanvasItemMaxScale, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemMaxScale, GetCanvasItemMaxScale, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, LockCanvasItem, LockCanvasItem, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnLockCanvasItem, UnLockCanvasItem, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, IsCanvasItemLocked, IsCanvasItemLocked, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemEditable, SetCanvasItemEditable, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemEditable, GetCanvasItemEditable, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemTranslate, SetCanvasItemTranslate, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemTranslate, GetCanvasItemTranslate, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemSizeF, GetCanvasItemSizeF, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemClip, SetCanvasItemClip, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemClip, GetCanvasItemClip, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, IsCanvasItemClip, IsCanvasItemClip, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnableCanvasItemClip, EnableCanvasItemClip, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemTransform, SetCanvasItemTransform, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemTransform, GetCanvasItemTransform, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, MoveCanvasItemZOrder, MoveCanvasItemZOrder, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemVisible, SetCanvasItemVisible, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemVisible, GetCanvasItemVisible, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, CanvasItemSaveAsFile, CanvasItemSaveAsFile, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemPreprocess, SetCanvasItemPreprocess, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetCanvasItemPreprocess, GetCanvasItemPreprocess, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemSourceClip, SetCanvasItemSourceClip, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, RegisterCanvasEventObserver, RegisterCanvasEventObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnregisterCanvasEventObserver, UnregisterCanvasEventObserver, dll);

        // transition
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateTransition, CreateTransition, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, DestroyTransition, DestroyTransition, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetTransitionProperty, SetTransitionProperty, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, AmbientEffect, AmbientEffect, dll);

        // canvas_item preview
        ASSIGN_API_FUNCTION_OR_BREAK(api, StartCanvasItemPreview, StartCanvasItemPreview, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StopCanvasItemPreview, StopCanvasItemPreview, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemPreviewParams, SetCanvasItemPreviewParams, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemPreviewPos, SetCanvasItemPreviewPos, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemPreviewFlipH, SetCanvasItemPreviewFlipH, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemPreviewFlipV, SetCanvasItemPreviewFlipV, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemPreviewRotate, SetCanvasItemPreviewRotate, dll);

        // visual
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumVisualSource, EnumVisualSource, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateVisual, CreateVisual, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, DestroyVisual, DestroyVisual, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, ReopenVisualAndUpdateTransforms, ReopenVisualAndUpdateTransforms, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVisualDestroyedWhenAllReferenceRemoved, SetVisualDestroyedWhenAllReferenceRemoved, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, RegisterVisualEventObserver, RegisterVisualEventObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnregisterVisualEventObserver, UnregisterVisualEventObserver, dll);

        // virtual camera
        ASSIGN_API_FUNCTION_OR_BREAK(api, StartVirtualCamera, StartVirtualCamera, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, StopVirtualCamera, StopVirtualCamera, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, SwitchVirtualCamera, SwitchVirtualCamera, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, RegisterVirtualCameraEventObserver, RegisterVirtualCameraEventObserver, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, UnregisterVirtualCameraEventObserver, UnregisterVirtualCameraEventObserver, dll);

        // filter
		ASSIGN_API_FUNCTION_OR_BREAK(api, CreateVisualFilter, CreateVisualFilter, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, DestroyVisualFilter, DestroyVisualFilter, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVisualFilterProperty, SetVisualFilterProperty, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, VisualFilterAction, VisualFilterAction, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, VisualFilterSetActive, VisualFilterSetActive, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, VisualFilterGetActive, VisualFilterGetActive, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVisualFiltersPriority, SetVisualFiltersPriority, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, AllocBuffer, AllocBuffer, dll)
        ASSIGN_API_FUNCTION_OR_BREAK(api, FreeBuffer, FreeBuffer, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, MediaSDKVersion, MediaSDKVersion, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetLogHandlerFunc, SetLogHandlerFunc, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, Initialize, Initialize, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateGlobalConfig, UpdateGlobalConfig, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, Uninitialize, Uninitialize, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, RegisterWindowEventObserver, RegisterWindowEventObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnregisterWindowEventObserver, UnregisterWindowEventObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, RegisterAudioObserver, RegisterAudioObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnregisterAudioObserver, UnregisterAudioObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetPreviewPosition, SetPreviewPosition, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAndClipPreviewPosition, SetAndClipPreviewPosition, dll);

		ASSIGN_API_FUNCTION_OR_BREAK(api, ShowPreviewWindow, ShowPreviewWindow, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, HidePreviewWindow, HidePreviewWindow, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, IsAllPreviewEnable, IsAllPreviewEnable, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, EnableAllPreview, EnableAllPreview, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, EnableTrack, EnableTrack, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, CreateProjector, CreateProjector, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, CloseProjector, CloseProjector, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetProjectorWndParams, SetProjectorWndParams, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetProjectorPosition, SetProjectorPosition, dll);

		ASSIGN_API_FUNCTION_OR_BREAK(api, SetUIConfig, SetUIConfig, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, EnableFineTuning, EnableFineTuning, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, GetVideoFileFrameInfo, GetVideoFileFrameInfo, dll);
		
		ASSIGN_API_FUNCTION_OR_BREAK(api, RemoveModel, RemoveModel, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateModelFPS, UpdateModelFPS, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateModelOutputSize, UpdateModelOutputSize, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateModelColorSpaceAndVideoRange, UpdateModelColorSpaceAndVideoRange, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, EnumEncoderSource, EnumEncoderSource, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, MockRenderHung, MockRenderHung, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, StartRenderProfiler, StartRenderProfiler, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, StartCollectPerformanceMatrics, StartCollectPerformanceMatrics, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, SetTTNtpMS, SetTTNtpMS, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumServiceSource, EnumServiceSource, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StartStream, StartStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StopStream, StopStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StartPushStream, StartPushStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StopPushStream, StopPushStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetLyraxLiveRoomId, SetLyraxLiveRoomId, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, OutputThumbnailSaveAs, OutputThumbnailSaveAs, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetStreamSEI, SetStreamSEI, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, ClearStreamSEI, ClearStreamSEI, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateStreamAbrOffset, UpdateStreamAbrOffset, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StartSpeedTestStream, StartSpeedTestStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StopSpeedTestStream, StopSpeedTestStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetStatisticInfo, GetStatisticInfo, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetEncoderStatisticInfo, GetEncoderStatisticInfo, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetEncoderPtsByStreamId, GetEncoderPtsByStreamId, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateABConfig, UpdateABConfig, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, VideoEncoderTargetBitrate, VideoEncoderTargetBitrate, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetRenderInfo, GetRenderInfo, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, TestEncoderSessionCountSupported, TestEncoderSessionCountSupported, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, GetVisualFPS, GetVisualFPS, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumVisualInput, EnumVisualInput, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, DoVisualInputAction, DoVisualInputAction, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, ReopenVisual, ReopenVisual, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumVisualFormat, EnumVisualFormat, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumAudioFormat, EnumAudioFormat, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVisualInputProperty, SetVisualInputProperty, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVisualSourceProperty, SetVisualSourceProperty, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetVisualSourceProperty, GetVisualSourceProperty, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetVisualInputProperty, GetVisualInputProperty, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, PauseVisualCapture, PauseVisualCapture, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, IsVisualCapturePause, IsVisualCapturePause, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, ContinueVisualCapture, ContinueVisualCapture, dll);
        
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateVirtualCameraProperty, UpdateVirtualCameraProperty, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVirtualCameraRotate, SetVirtualCameraRotate, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVirtualCameraFlipV, SetVirtualCameraFlipV, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetVirtualCameraFlipH, SetVirtualCameraFlipH, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, ForwardWindowMessage, ForwardWindowMessage, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, GetDefaultCaptureAudio, GetDefaultCaptureAudio, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetDefaultRenderAudio, GetDefaultRenderAudio, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumCaptureAudio, EnumCaptureAudio, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumRenderAudio, EnumRenderAudio, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateAudioInput, CreateAudioInput, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateLyraxAudioInput, CreateLyraxAudioInput, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateCustomAudioInput, CreateCustomAudioInput, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, DestroyAudioInput, DestroyAudioInput, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdatePCMAudioDatas, UpdatePCMAudioDatas, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioVolume, SetAudioVolume, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetAudioVolume, GetAudioVolume, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioInputParams, SetAudioInputParams, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioBalancing, SetAudioBalancing, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetAudioBalancing, GetAudioBalancing, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, IsAudioDownmixMono, IsAudioDownmixMono, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioDownmixMono, SetAudioDownmixMono, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioSyncOffset, SetAudioSyncOffset, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetAudioSyncOffset, GetAudioSyncOffset, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioMonitorType, SetAudioMonitorType, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetAudioMonitorType, GetAudioMonitorType, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioInterval, SetAudioInterval, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumAudioInputSource, EnumAudioInputSource, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnumAudioInput, EnumAudioInput, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioMute, SetAudioMute, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, IsAudioMute, IsAudioMute, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, AddAudioInputToTrack, AddAudioInputToTrack, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, RemoveAudioInputFromTrack, RemoveAudioInputFromTrack, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetAudioInputPerformance, GetAudioInputPerformance, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioInputReferenceId, SetAudioInputReferenceId, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioInputAECOption, SetAudioInputAECOption, dll);
        // ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioInputAGCOption, SetAudioInputAGCOption, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioInputRawDataOption, SetAudioInputRawDataOption, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioInputANSOption, SetAudioInputANSOption, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnableAudioInputEchoDetection, EnableAudioInputEchoDetection, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioInputRenderDeviceID, SetAudioInputRenderDeviceID, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioTrackDelayMs, SetAudioTrackDelayMs, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetGlobalRenderDeviceID, SetGlobalRenderDeviceID, dll);

        // rtc
        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateEngine, CreateEngine, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, DestroyEngine, DestroyEngine, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, RegisterRTCEventObserver, RegisterRTCEventObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnregisterRTCEventObserver, UnregisterRTCEventObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetBusinessId, SetBusinessId, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, JoinRoom, JoinRoom, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, LeaveRoom, LeaveRoom, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioProfile, SetAudioProfile, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, PublishStream, PublishStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnPublishStream, UnPublishStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SubscribeStream, SubscribeStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnSubscribeStream, UnSubscribeStream, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnableLocalAudio, EnableLocalAudio, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetPlaybackVolume, SetPlaybackVolume, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioPlaybackDevice, SetAudioPlaybackDevice, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetAudioPlaybackDevice, GetAudioPlaybackDevice, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnableAudioPropertiesReport, EnableAudioPropertiesReport, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EnableLocalVideo, EnableLocalVideo, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateCropAndScale, UpdateCropAndScale, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateExcludedVisuals, UpdateExcludedVisuals, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, SetRemoteVideoConfig, SetRemoteVideoConfig, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SendSEIMessage, SendSEIMessage, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StartForwardStreamToRooms, StartForwardStreamToRooms, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateForwardStreamToRooms, UpdateForwardStreamToRooms, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StopForwardStreamToRooms, StopForwardStreamToRooms, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StartLiveTranscoding, StartLiveTranscoding, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UpdateLiveTranscoding, UpdateLiveTranscoding, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StopLiveTranscoding, StopLiveTranscoding, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SendRoomMessage, SendRoomMessage, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SendUserMessage, SendUserMessage, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, EffectPlatformInitialize, EffectPlatformInitialize, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EffectPlatformUninitialize, EffectPlatformUninitialize, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EffectPlatformLoadModels, EffectPlatformLoadModels, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EffectPlatformUpdateConfig, EffectPlatformUpdateConfig, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EffectPlatformRegistObserver, EffectPlatformRegistObserver, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, EffectPlatformUnregistObserver, EffectPlatformUnregistObserver, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, CreateAudioFilter, CreateAudioFilter, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, DestroyAudioFilter, DestroyAudioFilter, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioFilterEnable, SetAudioFilterEnable, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, IsAudioFilterEnable, IsAudioFilterEnable, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, AudioFilterAction, AudioFilterAction, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, SetAudioFilterProperty, SetAudioFilterProperty, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, GetAudioFilterProperty, GetAudioFilterProperty, dll);

        // VQM
        ASSIGN_API_FUNCTION_OR_BREAK(api, InitVQM, InitVQM, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, UnInitVQM, UnInitVQM, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, QueryGoLiveRecommendedParams, QueryGoLiveRecommendedParams, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, QueryCameraRecommendedParams, QueryCameraRecommendedParams, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, QueryGoLiveManuallySelectedParams, QueryGoLiveManuallySelectedParams, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, QueryCameraBestParamsForTarget, QueryCameraBestParamsForTarget, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, StartAdaptiveGearStrategyReport, StartAdaptiveGearStrategyReport, dll);

        ASSIGN_API_FUNCTION_OR_BREAK(api, GetVisualFrame, GetVisualFrame, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, IsStreamInProcess, IsStreamInProcess, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, ReconfigVideoOutput, ReconfigVideoOutput, dll);
        ASSIGN_API_FUNCTION_OR_BREAK(api, FallbackVideoEncoder, FallbackVideoEncoder, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, SetCanvasItemPreprocessDefaultSize, SetCanvasItemPreprocessDefaultSize, dll);
		ASSIGN_API_FUNCTION_OR_BREAK(api, RemoveCanvasItemPreprocessDefaultSize, RemoveCanvasItemPreprocessDefaultSize, dll);
        success = true;

    } while (0);

    return success;
}

MediaSDKControllerImplV2* MediaSDKControllerV2ImplInit()
{
    std::unique_lock<std::mutex> lock(*g_init_mutex);
	bool ok = MediaSDKV2LoadSymbols(&g_sdk_api);
	if (!ok)
		return nullptr;

    g_sdkController = new MediaSDKControllerImplV2();
    return g_sdkController;
}

void MediaSDKControllerV2ImplUninit()
{
    std::unique_lock<std::mutex> lock(*g_init_mutex);
	if (g_sdkController)
	{
		delete g_sdkController;
		g_sdkController = nullptr;
	}
}

MediaSDKControllerImplV2::MediaSDKControllerImplV2()
{
    sdk_callback_mgr_ = std::make_unique<SDKCallBackMgr>(call_helper_);
    rtc_helper_ = std::make_unique<RTCLiveHelper>(&g_sdk_api, call_helper_);
    hook_api_layer_ = std::make_unique<v3::HookAPILayer>();
    audio_output_buffer_ = std::make_unique<v3::SharedMemoryRingBuffer>();
}

MediaSDKControllerImplV2::~MediaSDKControllerImplV2(){}

std::mutex* g_log_handler_mutex = new std::mutex();
sdk_helper::LogMessageHandler* g_log_message_handler_v2 = new LogMessageHandler();
bool                           g_async_log = false;

void MediaSDKControllerImplV2::SetLogMessageHandler(sdk_helper::LogMessageHandler handler, bool async_log /* = false */)
{
    std::unique_lock<std::mutex> lock(*g_log_handler_mutex);
    *g_log_message_handler_v2 = handler;
    g_async_log = async_log;
}

void MediaSDKControllerImplV2::SetThreadMonitorEventHandler(ThreadMonitorEventHandler handler)
{
    sdk_callback_mgr_->SetThreadMonitorEventHandler(handler);
}

static HANDLE g_hFile = NULL;

static bool LogMessageHandlerV2(LogSeverity severity, const LPCSTR szText)
{
    if (szText == nullptr) 
        return false;
    
    if (g_async_log)
    {
        if (g_cief && g_cief->GetThreadMgr())
        {
            std::string logText(szText);
            g_cief->GetThreadMgr()->AddTaskToThreadPool(new NativeTask([severity, logText]() {
                std::unique_lock<std::mutex> lock(*g_log_handler_mutex);
                if (g_log_message_handler_v2 != NULL)
                {
                    (*g_log_message_handler_v2)(severity, nullptr, 0, logText.c_str()); // sdkv2 日志上报
                }

                if (g_hFile != NULL && g_hFile != INVALID_HANDLE_VALUE)
                {
                    DWORD bytesWritten;
                    ::WriteFile(g_hFile, logText.c_str(), static_cast<DWORD>(logText.length()), &bytesWritten, NULL);
                    ::FlushFileBuffers(g_hFile); // 强制刷新缓冲区确保日志及时写入
                }
            }));
        }
    }
    else
    {
        std::unique_lock<std::mutex> lock(*g_log_handler_mutex);
        if (g_log_message_handler_v2 == NULL)
        {
            return false;
        }
        (*g_log_message_handler_v2)(severity, nullptr, 0, szText); // sdkv2日志上报
        ::WriteFile(g_hFile, szText, strlen(szText), NULL, NULL);
    }
    
    return true;
}

BOOL InitializeLogFile(LPCWSTR pzFile)
{
    g_hFile = CreateFileW(pzFile, FILE_APPEND_DATA, FILE_ATTRIBUTE_NORMAL, NULL, OPEN_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (!g_hFile || g_hFile == INVALID_HANDLE_VALUE)
    {
        g_hFile = CreateFileW(pzFile, FILE_APPEND_DATA, FILE_SHARE_READ | FILE_SHARE_WRITE, NULL, OPEN_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
        if (!g_hFile || g_hFile == INVALID_HANDLE_VALUE)
        {
            return false;
        }
    }

    return true;
}

static bool SafeWriteFrameData(void* dst, const void* src, const int32_t width, const int32_t height, const VIDEO_PIXEL_FORMAT format, int32_t row_pitch)
{
    bool success = false;

    if (row_pitch == 0)
        row_pitch = width;

    __try
    {
        if (format == VIDEO_PIXEL_FORMAT::PIXEL_FORMAT_BGRA)
        {
            for (int32_t h = 0; h < height; ++h) {
                const uint8_t* src_addr = reinterpret_cast<const uint8_t*>(src) + row_pitch * h;
                uint8_t* dst_addr = reinterpret_cast<uint8_t*>(dst) + 3 * width * h;
                for (int32_t w = 0; w < width; ++w)
                {
                    int32_t w3 = 3 * w;
                    int32_t w4 = 4 * w;

                    dst_addr[w3 + 2] = src_addr[w4 + 0]; // B
                    dst_addr[w3 + 1] = src_addr[w4 + 1]; // G
                    dst_addr[w3 + 0] = src_addr[w4 + 2]; // R
                }
            }
            success = true;
        }
      
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        success = false;
    }
    return success;
}

void MediaSDKControllerImplV2::SetHost(const std::string& host)
{
    host_info_ = host;
}

void MediaSDKControllerImplV2::UpdateHost(const std::string& host)
{
    host_info_ = host;

    std::string init_json_str;
    try
    {
        auto init_json = nlohmann::json::parse(init_json_);
        init_json["parfait_host"] = host_info_;
        init_json_str = init_json.dump();
    }
    catch (...)
    {
        LOG(ERROR) << "MediaSDKControllerImplV2::UpdateHost fail, init_json_ error!";
    }

    init_json_ = init_json_str;

    call_helper_.SyncCall<bool>(g_sdk_api.UpdateGlobalConfig, init_json_str.c_str());
}

bool MediaSDKControllerImplV2::Init(const INITIALIZE_INFO& param)
{
    ScopeCallTimeCal tcf(true, "MediaSDKControllerImplV2::Init");
    bool             success = false;
    std::wstring     log_path;
    try
    {
        auto p = std::filesystem::u8path(param.sdkLogFile);
        log_path = p.wstring();
    }
    catch (...)
    {
        LOG(ERROR) << " [MediaSDKControllerImplV2::Init] get log_path fail !";
    }

    if (log_path.empty())
        log_path = GetDefaultLogFile();

    if (!InitializeLogFile(log_path.c_str()))
    {
        LOG(ERROR) << " [MediaSDKControllerImplV2::Init] InitializeLogFile fail !";
        return false;
    }
    std::string init_json_str = "";
    try
    {
        nlohmann::json init_json = nlohmann::json::parse(param.json);
        init_json["version"] = param.version;
        init_json["uid"] = param.uid;
        init_json["did"] = param.did;
        init_json["parfait_host"] = host_info_;
        init_json_str = init_json.dump();
    }
    catch (...)
    {
        LOG(ERROR) << "set init_json error!";
    }

    init_json_ = init_json_str;

    g_sdk_api.SetLogHandlerFunc(LogMessageHandlerV2);

    StartConnectAudioCallback();

    hook_api_layer_->GetAudioFrameProcessor()->SetAddCustomAudioInputHandler(
        [this](const std::string& audio_id, uint32_t track_id, mediasdk::hook_api::CustomAudioInputDelegate* delegate) {
            bool ok = call_helper_.SyncCall<bool>(g_sdk_api.CreateCustomAudioInput, audio_id.c_str(), track_id, delegate);
            if (track_id > 0)
            {
                audio_id_map_[audio_id] = 1 << (track_id - 1);
            }
            return ok;
        });

    hook_api_layer_->GetAudioFrameProcessor()->SetRemoveCustomAudioInputHandler(
        [this](const std::string& audio_id) {
            auto success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyAudioInput, audio_id.c_str());
            if (success && audio_id_map_.count(audio_id))
            {
                audio_id_map_.erase(audio_id);
            }
        });

    hook_api_layer_->GetAudioFrameProcessor()->SetMonoAudioPCMS16DataCallback(
        std::bind(&MediaSDKControllerImplV2::OnMonoAudioPCMS16DataCallback,
                  this, std::placeholders::_1, std::placeholders::_2,
                  std::placeholders::_3, std::placeholders::_4, std::placeholders::_5));

    hook_api_layer_->GetAudioFrameProcessor()->SetSetMainAudioTrackDelayHandler(
        [this](const int64_t delay_ms) -> bool {
            LOG(INFO) << StringPrintf("MediaSDKControllerImplV2 -> SetAudioTrackDelayMs : %lld", delay_ms);
            g_sdk_api.SetAudioTrackDelayMs(1, delay_ms);
            return true;
        });

    std::mutex init_ready_mutex;
    std::condition_variable init_ready_cv;
    bool                    init_ready = false;

    hook_api_layer_->Init([&init_ready_mutex, &init_ready_cv, &init_ready]() {
        std::unique_lock<std::mutex> lock(init_ready_mutex);
        init_ready = true;
        init_ready_cv.notify_one();
    });

    do
    {
        bool ok = false;

        ok = call_helper_.SyncCall<bool>(g_sdk_api.Initialize, sdk_callback_mgr_.get(), init_json_str.c_str(), hook_api_layer_.get());
        if (!ok)
            break;

        ok = call_helper_.SyncCall<bool>(g_sdk_api.RegisterWindowEventObserver, sdk_callback_mgr_.get());
        if (!ok)
            break;

        ok = call_helper_.SyncCall<bool>(g_sdk_api.RegisterAudioObserver, sdk_callback_mgr_.get());
        if (!ok)
            break;

        ok = call_helper_.SyncCall<bool>(g_sdk_api.RegisterVisualEventObserver, sdk_callback_mgr_.get());
        if (!ok)
            break;

        ok = call_helper_.SyncCall<bool>(g_sdk_api.RegisterCanvasEventObserver, sdk_callback_mgr_.get());
        if (!ok)
            break;

        ok = call_helper_.SyncCall<bool>(g_sdk_api.RegisterVirtualCameraEventObserver, sdk_callback_mgr_.get());
        if (!ok)
            break;

		g_video_fps = param.fps;

        has_init_ = true;
        success = true;

    } while (0);

    {
        LOG(INFO) << "MediaSDKControllerImplV2::Init, waiting v3::HookAPILayer init";
        std::unique_lock<std::mutex> lock(init_ready_mutex);
        while (!init_ready)
        {
            init_ready_cv.wait(lock);
        }
        LOG(INFO) << "MediaSDKControllerImplV2::Init, v3::HookAPILayer ready!";
    }

    return success;
}

#define EXPAND_TO_STRING(s) #s

#define CHECK_INITIALIZED_AND_PRINT_LOG(logNow, func)                                                       \
    int wait_times = 0;                                                                                     \
    while (!has_init_)                                                                                      \
    {                                                                                                       \
        LOG(INFO) << "SDK has not been initialized yet. Please wait... Current function: " << __FUNCTION__; \
    }                                                                                                       \
    std::string      funcName(EXPAND_TO_STRING(func)[0] == '0' ? __FUNCTION__ : EXPAND_TO_STRING(func));    \
    ScopeCallTimeCal tcf(logNow, funcName);

void MediaSDKControllerImplV2::Uninit()
{
    LOG(INFO) << "[MediaSDKControllerImplV2::Uninit()] begin ";
    
    StopConnectAudioCallback();

    hook_api_layer_->GetAudioFrameProcessor()->SetAddCustomAudioInputHandler(NULL);
    hook_api_layer_->GetAudioFrameProcessor()->SetRemoveCustomAudioInputHandler(NULL);
    hook_api_layer_->GetAudioFrameProcessor()->SetMonoAudioPCMS16DataCallback(NULL);

    LOG(INFO) << "[MediaSDKControllerImplV2::Uninit()] hook_api_layer_->Uninit begin";
    hook_api_layer_->Uninit();
    LOG(INFO) << "[MediaSDKControllerImplV2::Uninit()] hook_api_layer_->Uninit end";

    call_helper_.SyncCall<bool>(g_sdk_api.UnregisterAudioObserver, sdk_callback_mgr_.get());
    call_helper_.SyncCall<bool>(g_sdk_api.UnregisterWindowEventObserver, sdk_callback_mgr_.get());
    call_helper_.SyncCall<bool>(g_sdk_api.UnregisterVisualEventObserver, sdk_callback_mgr_.get());
    call_helper_.SyncCall<bool>(g_sdk_api.UnregisterCanvasEventObserver, sdk_callback_mgr_.get());
    call_helper_.SyncCall<bool>(g_sdk_api.UnregisterVirtualCameraEventObserver, sdk_callback_mgr_.get());
    call_helper_.SyncCall<bool>(g_sdk_api.Uninitialize);
    CloseHandle(g_hFile);
    LOG(INFO) << "[MediaSDKControllerImplV2::Uninit()] end ";
}

const char* DSHOW_ENUM_BLACK_LIST[] = {"LSVCam", "WebcastMate VirtualCamera"};
const char* DSHOW_CREATE_BLACK_LIST[] = {"WebcastMate VirtualCamera"};

static bool InDshowEnumBlackList(const char* device_id)
{
    int size = sizeof(DSHOW_ENUM_BLACK_LIST) / sizeof(DSHOW_ENUM_BLACK_LIST[0]);
    for (int i = 0; i < size; i++)
    {
        if (strcmp(DSHOW_ENUM_BLACK_LIST[i], device_id) == 0)
        {
            return true;
        }
    }
    return false;
}

static bool InDshowCreateBlackList(const char* device_id)
{
    int size = sizeof(DSHOW_CREATE_BLACK_LIST) / sizeof(DSHOW_CREATE_BLACK_LIST[0]);
    for (int i = 0; i < size; i++)
    {
        if (strcmp(DSHOW_CREATE_BLACK_LIST[i], device_id) == 0)
        {
            return true;
        }
    }
    return false;
}

bool MediaSDKControllerImplV2::EnumWindows(const bool need_icon, std::vector<WINDOW_DESC>* output)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json input_json;
    input_json["include_minimize_windows"] = true;
    input_json["include_icon"] = need_icon;

    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumVisualInput, game_visual_source::GetPluginName().data(), input_json.dump().c_str());

    bool success = false;

    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            for (const auto& element : output_json)
            {
                WINDOW_DESC wininfo;
                if (!element.contains("exe_name"))
                    continue;

                wininfo.szClass = element.value("class_name", "");
                wininfo.szTitle = element.value("title", "");
                wininfo.hwnd = reinterpret_cast<HWND>(element.value("id", (uint64_t)0));
                wininfo.szEXE = element.value("exe_name", "");
                wininfo.dwPID = element.value("pid", (uint32_t)0);
                wininfo.iconBase64 = element.value("icon_base64", "");
                wininfo.isMinimized = element.value("minimize", false);
                RECT rect{};
                GetClientRect(wininfo.hwnd, &rect);
                wininfo.width = rect.right - rect.left;
                wininfo.height = rect.bottom - rect.top;
                if (output)
                    output->push_back(wininfo);
            }
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse EnumWindows output error!";
    }

    return success;
}

bool MediaSDKControllerImplV2::CreateWindowSource(const std::string visual_id, const WINDOW_SOURCE& window, VISUAL_CAPTURE_TYPE* cap_type, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = desktop_capture_visual_source::GetPluginName();
    params.audio_track_id = 0;
    nlohmann::json json_root;
    json_root["capture_type"] =
        desktop_capture_visual_source::kCaptureVisualTypeWindow;
    json_root["show_cursor"] = window.windowDesc.hasCursor;
    json_root["id"] = reinterpret_cast<uint64_t>(window.windowDesc.hwnd);
    json_root["class_name"] = window.windowDesc.szClass;
    json_root["exe_name"] = window.windowDesc.szEXE;
    json_root["capture_method"] = 0;
    json_root["capture_id"] = (int32_t)window.windowDesc.dwPID;
    params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
    params.destroy_when_all_ref_removed = true;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_WINDOW;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create WindowSource error!";
        return false;
    }

    if (cap_type)
    {
        *cap_type = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_WGC;
    }

    return success;
}

bool MediaSDKControllerImplV2::EnumMonitors(std::vector<MONITOR_DESC>* output)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["method"] = desktop_capture_visual_source::EnumMethod::kEnumMethodScreens;
    json_root["include_minimize_windows"] = false;
    json_root["include_icon"] = false;

    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumVisualInput, desktop_capture_visual_source::kDesktopCaptureVisualSourceName, json_root.dump().c_str());

    bool success = false;

    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;

            if (output_json.contains("screens") && output)
            {
                int32_t index = 0;
                for (const auto& element : output_json["screens"])
                {
                    MONITOR_DESC info;
                    info.did = element.value("device_id", "");
                    info.index = index++;
                    info.rect.X = 0;
                    info.rect.Y = 0;
                    info.rect.Width = element.value("cx", 0) - info.rect.X;
                    info.rect.Height = element.value("cy", 0) - info.rect.Y;
                    info.degree = 0; // TODO
                    output->push_back(info);
                }
            }

            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse EnumMonitors output error!";
    }

    return success;
}

bool MediaSDKControllerImplV2::CreateMonitorSource(const std::string visual_id, const MONITOR_SOURCE& monitor, VISUAL_CAPTURE_TYPE* cap_type, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = desktop_capture_visual_source::GetPluginName();
    params.audio_track_id = 0;
    {
        nlohmann::json json_root;
        json_root["capture_type"] =
            desktop_capture_visual_source::kCaptureVisualTypeScreen;
        json_root["show_cursor"] = monitor.hasCursor;
        json_root["device_id"] = monitor.monitorDesc.did;
        json_root["capture_method"] = 0;
        params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
        params.destroy_when_all_ref_removed = true;
    }
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_MONITOR;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create MonitorSource error!";
        return false;
    }

    if (cap_type)
    {
        *cap_type = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_DDA;
    }

    return success;
}

bool MediaSDKControllerImplV2::WindowSourceSelectWindow(const std::string& visual_id, int64_t win_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["id"] = static_cast<uint64_t>(win_id);
    json_root["action"] = desktop_capture_visual_source::kDesktopCaptureVisualSourceSelect;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::WindowSourceSelectWindow(const std::string& visual_id, const std::string& class_name)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["class_name"] = class_name;
    json_root["action"] = desktop_capture_visual_source::kDesktopCaptureVisualSourceSelect;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::WindowSourceShowCursor(const std::string& visual_id, const bool show)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_params;
    json_params["visible_cursor"] = show;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(),
                                               desktop_capture_visual_source::kDesktopCaptureVisualSourceVisibleCursor, json_params.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::WindowSourceIsShowCursor(const std::string& visual_id, bool* show)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["key"] = desktop_capture_visual_source::kDesktopCaptureVisualSourceCursorCheck;
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), json_root.dump().c_str());
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            if (show)
                *show = output_json["visible"];
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse WindowSourceIsShowCursor output error!";
    }

    return success;
}

bool MediaSDKControllerImplV2::MonitorSourceShowCursor(const std::string& visual_id, const bool show)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    return WindowSourceShowCursor(visual_id, show);
}

bool MediaSDKControllerImplV2::MonitorSourceIsShowCursor(const std::string& visual_id, bool* show)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    return WindowSourceIsShowCursor(visual_id, show);
}

bool MediaSDKControllerImplV2::MonitorSourceSetMonitor(const std::string& visual_id, const std::string& did)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["device_id"] = did;
    json_root["action"] = desktop_capture_visual_source::kDesktopCaptureVisualSourceSelect;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::AddSourceInfo(const std::string& visual_id, const bool detached)
{
    std::unique_lock<std::mutex> lock(source_info_map_mutex_);
    auto found = source_info_map_.find(visual_id);
    if (found != source_info_map_.end())
    {
        return false;
    }

    auto& new_source_info = source_info_map_[visual_id];
    new_source_info.visual_id = visual_id;
    new_source_info.is_duplicate_source = false;
    new_source_info.ref_count = 0;
    new_source_info.detach = detached;

    return true;
}

bool MediaSDKControllerImplV2::RemoveSourceInfo(const std::string& visual_id)
{
    std::unique_lock<std::mutex> lock(source_info_map_mutex_);
    if (source_info_map_.count(visual_id))
    {
        source_info_map_.erase(visual_id);
    }

    return true;
}

bool MediaSDKControllerImplV2::CreateSamiNoiseSuppressAudioFilterActually(const std::string& audio_id, const std::string& filter_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (!Sami_create_flag_)
    {
        LOG(ERROR) << " Please call MediaSDKControllerImplV2::CreateSamiNoiseSuppressAudioFilter first!";
        return false;
    }
    if (Sami_filter_id_map_.count(filter_id) != 0)
    {
        return true; // 已经创建过了不再创建
    }
    nlohmann::json json_param;
    json_param["enable"] = true;
    json_param["noise_suppress_level"] = 50;
    json_param["model_file_path"] = SamiNS_model_path_map_[filter_id];
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateAudioFilter, filter_id.c_str(), "SAMINoiseSuppressAudioFilter",
                                               audio_id.c_str(), json_param.dump().c_str());
    if (success)
    {
        Sami_filter_id_map_[filter_id] = audio_id;
    }
    return success;
}

void MediaSDKControllerImplV2::SetPreviewDefaultSetting(int32_t track_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::PreviewUIConfig params = {0xFE2C55FF, 0xFE2C55FF, 0xFF3B5CFF, 0xFF3B5CFF};
    bool                      success = call_helper_.SyncCall<bool>(g_sdk_api.SetUIConfig, track_id, params);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::SetPreviewDefaultSetting] SetUIConfig error! track_id = " << track_id;
    }

    success = call_helper_.SyncCall<bool>(g_sdk_api.EnableFineTuning, track_id, true);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::SetPreviewDefaultSetting] EnableFineTuning error! track_id = " << track_id;
    }
}

bool MediaSDKControllerImplV2::AudioSourceSetTracksActually(const std::string& audio_id, const std::vector<int>& addVec, const std::vector<int>& removeVec)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::ResultBoolBool ret;
    for (const auto it : addVec)
    {
        ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.AddAudioInputToTrack, audio_id.c_str(), it);
        if (!ret.success || !ret.value)
        {
            LOG(ERROR) << "[MediaSDKControllerImplV2::AudioSourceSetTracksActually] AddAudioInputToTrack error! track_id = " << it;
            return false;
        }
    }

    for (const auto it : removeVec)
    {
        ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.RemoveAudioInputFromTrack, audio_id.c_str(), it);
        if (!ret.success || !ret.value)
        {
            LOG(ERROR) << "[MediaSDKControllerImplV2::AudioSourceSetTracksActually] RemoveAudioInputFromTrack error! track_id = " << it;
            return false;
        }
    }

    return true;
}

bool MediaSDKControllerImplV2::CreateGameSource(const std::string visual_id, const GAME_SOURCE& game, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = game_visual_source::GetPluginName();
    params.audio_track_id = 0;
    {
        nlohmann::json json_root;
        json_root["show_cursor"] = game.windowDesc.hasCursor;
        json_root["id"] = reinterpret_cast<uint64_t>(game.windowDesc.hwnd);
        json_root["class_name"] = game.windowDesc.szClass;
        json_root["exe_name"] = game.windowDesc.szEXE;
        if (game.enableLimit)
        {
            json_root["fps"] = game.limitFps;
        }
        else
        {
            json_root["fps"] = game.limitFps * 2;
        }
        json_root["error_report_time_s"] = game.errorReportTime;
        json_root["hook_used_cpu"] = game.enableCpu;
        params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
        params.destroy_when_all_ref_removed = true;
    }
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_MONITOR;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create GameSource error!";
        return false;
    }
    {
        std::unique_lock<std::mutex> lock(game_source_info_mutex_);
        game_source_info_[visual_id].show_cursor = game.windowDesc.hasCursor;
    }

    return success;
}

bool MediaSDKControllerImplV2::GameSourceShowCursor(const std::string& visual_id, const bool show)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_params;
    json_params["enbale_cursor"] = show;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(),
                                               game_visual_source::kGameVisualSourceEnbaleCursor, json_params.dump().c_str());
    if (success)
    {
        std::unique_lock<std::mutex> lock(game_source_info_mutex_);
        game_source_info_[visual_id].show_cursor = show;
    }
    return success;
}

bool MediaSDKControllerImplV2::GameSourceIsShowCursor(const std::string& visual_id, bool* show)
{

    std::unique_lock<std::mutex> lock(game_source_info_mutex_);
    auto                         found = game_source_info_.find(visual_id);
    if (found == game_source_info_.end())
        return false;

    if (show)
        *show = found->second.show_cursor;

    return true;
}

bool MediaSDKControllerImplV2::GameSourceSelectTarget(const std::string visual_id, const GAME_SOURCE& game)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["show_cursor"] = game.windowDesc.hasCursor;
    json_root["id"] = reinterpret_cast<uint64_t>(game.windowDesc.hwnd);
    json_root["class_name"] = game.windowDesc.szClass;
    json_root["exe_name"] = game.windowDesc.szEXE;
    if (game.enableLimit)
    {
        json_root["fps"] = game.limitFps;
    }
    else
    {
        json_root["fps"] = game.limitFps * 2;
    }
    json_root["error_report_time_s"] = game.errorReportTime;
    json_root["hook_used_cpu"] = game.enableCpu;
    json_root["action"] = game_visual_source::kGameVisualSourceSelect;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::GameSourceLimitCaptureFPS(const std::string& visual_id, const bool enable_limit_fps, const uint32_t limit_fps)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_params;
    if (enable_limit_fps)
    {
        json_params["set_capture_fps"] = limit_fps;
    }
    else
    {
        json_params["set_capture_fps"] = limit_fps * 2;
    }
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), game_visual_source::kGameVisualSourceSetCaptureFPS, json_params.dump().c_str());

    return success;
}

bool MediaSDKControllerImplV2::GameSourceSetShareDataMode(const std::string& visual_id, bool enable_cpu)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_params;
    json_params["enbale_cpu_buffer"] = enable_cpu;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), game_visual_source::kGameVisualSourceEnbaleCPUBuffer, json_params.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateImageSource(const std::string visual_id, const IMAGE_SOURCE& image, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = image_visual_source::GetPluginName();
    params.audio_track_id = 0;
    {
        nlohmann::json json_root;
        json_root["file_path"] = image.materialDesc.path;
        params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
        params.destroy_when_all_ref_removed = true;
    }
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_IMAGE;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create ImageSource error!";
        return false;
    }

    return success;
}

bool MediaSDKControllerImplV2::ImageSourceSetFile(const std::string& visual_id, const std::string& image_filepath)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_params;
    json_params["file_path"] = image_filepath;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), image_visual_source::kImageVisualSourceSetFile, json_params.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateMediaSource(const std::string visual_id, const FAV_SOURCE& fav, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = fav_visual_source::GetPluginName();
    uint32_t track_ids = fav.audioTrack;
    params.audio_track_id = 0;
    bool res = GetFirstAudioTrackOfTrackIds(track_ids, params.audio_track_id);
    if (!res)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateMediaSource] GetFirstAudioTrackOfTrackIds error! param.audioTrack = " << fav.audioTrack;
    }
    if (params.audio_track_id > 0)
    {
        audio_id_map_[visual_id] = 1 << (params.audio_track_id - 1);
    }
    {
        nlohmann::json json_root;
        json_root["url"] = fav.materialDesc.path;
        json_root["hw"] = fav.enableHardDecode;
        json_root["loop"] = fav.materialDesc.loop;
        json_root["start_up_seconds"] = fav.seekTime;
		if (fav.readPacketTimeoutMS.has_value())
		{
			json_root["read_packet_time_out_ms"] = fav.readPacketTimeoutMS.value();
		}
        if (fav.disablePostDriverTask.has_value())
        {
            json_root["disable_post_driver_task"] = fav.disablePostDriverTask.value();
        }
        if (fav.audioRenderDeviceId.has_value())
        {
            json_root["audio_render_device_id"] = fav.audioRenderDeviceId.value();
        }
        params.json_params = json_root.dump();
        params.destroy_when_all_ref_removed = true;
    }
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_FAV;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create MediaSource error!";
        return false;
    }
    audio_track_not_remove_.insert(visual_id);

    return success;
}

bool MediaSDKControllerImplV2::MediaSourcePause(const std::string& visual_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.PauseVisualCapture, visual_id.c_str());
    return success;
}

bool MediaSDKControllerImplV2::MediaSourceIsPaused(const std::string& visual_id, bool* is_paused)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::ResultBoolBool ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.IsVisualCapturePause, visual_id.c_str());
    if (ret.success && is_paused)
    {
        *is_paused = ret.value;
    }

    return ret.success;
}

bool MediaSDKControllerImplV2::MediaSourceResume(const std::string& visual_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.ContinueVisualCapture, visual_id.c_str());
    return success;
}

bool MediaSDKControllerImplV2::MediaSourceIsSeekable(const std::string& visual_id, bool* is_seekable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), fav_visual_source::kFAVVisualPropertyCanSeek);
    std::string              str = result.ToString();
    if (is_seekable)
    {
        *is_seekable = static_cast<bool>(std::stoi(str));
    }
    return true;
}

bool MediaSDKControllerImplV2::MediaSourceSeek(const std::string& visual_id, const int64_t position_sec, bool absolute)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["action"] = fav_visual_source::kFAVVisualActionSeek;
    json_root["pos"] = position_sec;
    json_root["absolute"] = absolute;
    std::string json = json_root.dump();

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json.c_str());
    return success;
}

bool MediaSDKControllerImplV2::MediaSourceGetPosition(const std::string& visual_id, double* position)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), fav_visual_source::kFAVVisualActionSeek);
    std::string              str = result.ToString();
    if (position)
    {
        *position = std::stod(str);
    }
    return true;
}

bool MediaSDKControllerImplV2::MediaSourceGetDuration(const std::string& visual_id, int64_t* duration_1_1000000_sec)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), fav_visual_source::kFAVVisualPropertyDuration);
    std::string              str = result.ToString();
    if (duration_1_1000000_sec)
    {
        *duration_1_1000000_sec = std::stoll(str) * 1000000;
    }
    return true;
}

bool MediaSDKControllerImplV2::MediaSourceEnableLoop(const std::string& visual_id, const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["action"] = fav_visual_source::kFAVVisualActionEnableLoop;
    json_root["on"] = enable;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::MediaSourceEnableHardwareAcceleration(const std::string& visual_id, const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["hw"] = enable;
    auto success = call_helper_.SyncCall<bool>(g_sdk_api.ReopenVisual, visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::MediaSourceResetFile(const std::string& visual_id, const std::string& filepath)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["url"] = filepath;
    auto success = call_helper_.SyncCall<bool>(g_sdk_api.ReopenVisual, visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::TextSourceGetFonts(std::vector<std::string>* font_list)
{
    if (!font_list)
    {
        LOG(ERROR) << "[ MediaSDKControllerImplV2::TextSourceGetFonts] font_list is nullptr";
        return false;
    }
    Gdiplus::InstalledFontCollection collection;
    INT32                            count = collection.GetFamilyCount();
    if (count <= 0)
        return false;
    TinyScopedPtr<Gdiplus::FontFamily, DefaultArrayDeleter<Gdiplus::FontFamily>> families(new Gdiplus::FontFamily[count]);
    INT32                                                                        iRes = 0;
    if (collection.GetFamilies(count, families, &iRes) != Gdiplus::Ok)
        return false;
    TCHAR szFontName[MAX_PATH] = {0};
    for (INT32 i = 0; i < iRes; ++i)
    {
        memset(szFontName, 0, sizeof(szFontName));
        if (families[i].GetFamilyName(szFontName) == Gdiplus::Ok)
        {
            std::wstring wsFontName(szFontName);
            std::string  fontName = WStringToUTF8(wsFontName);
            font_list->push_back(fontName);
        }
    }
    return true;
}

bool MediaSDKControllerImplV2::CreateExtrenalBrowserSource(const std::string visual_id, const BROWSER_SOURCE& browser, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (browser.captureDLL.empty() && browser.token.empty() && browser.deviceID == 0)
        return false;

    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = browser_visual_source::GetPluginName();
    params.audio_track_id = 0;
    {
        nlohmann::json json_root;
        json_root["token"] = browser.token;
        json_root["capture_dll_path"] = browser.captureDLL;
        json_root["device_id"] = browser.deviceID;
        params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
        params.destroy_when_all_ref_removed = true;
    }
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_BROWSER;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create ExtrenalBrowserSource error!";
        return false;
    }

    return success;
}

bool MediaSDKControllerImplV2::EnumVideoInputDevices(std::vector<DSHOW>* device_info_list)
  {
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (!device_info_list)
        return false;
    device_info_list->clear();
    nlohmann::json json_root;
    json_root["type"] = dshow_visual_source::kCaptureTypeCamera;
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumVisualInput, dshow_visual_source::GetPluginName().data(), json_root.dump().c_str());
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            if (!output_json.contains("camera_video"))
                break;
            auto video_json = output_json["camera_video"];
            for (const auto& element : video_json)
            {
                DSHOW device_info;
                device_info.id = element["device_id"];
                device_info.name = element["device_name"];

                if (InDshowCreateBlackList(device_info.id.c_str()))
                    continue;

                device_info_list->push_back(std::move(device_info));
            }
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse EnumVideoInputDevices output error!";
    }

    return success;
}

bool MediaSDKControllerImplV2::EnumAudioInputDevices(std::vector<DSHOW>* device_info_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (!device_info_list)
        return false;
    device_info_list->clear();
    nlohmann::json json_root;
    json_root["type"] = dshow_visual_source::kCaptureTypeAnalog;
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumVisualInput, dshow_visual_source::GetPluginName().data(), json_root.dump().c_str());
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            if (!output_json.contains("analog_audio"))
                break;
            auto audio_json = output_json["analog_audio"];
            for (const auto& element : audio_json)
            {
                DSHOW device_info;
                device_info.id = element["device_id"];
                device_info.name = element["device_name"];
                device_info_list->push_back(std::move(device_info));
            }
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse EnumAudioInputDevices output error!";
    }

    return success;
}

bool MediaSDKControllerImplV2::EnumDeviceSupportFormats(const DSHOW& device_info, std::vector<VIDEO_CAPTURE_FORMAT>* video_format_list, std::vector<AUDIO_CAPTURE_FORMAT>* audio_format_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (!video_format_list)
    {
        return false;
    }
    video_format_list->clear();
    auto result_video = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumVisualFormat, dshow_visual_source::GetPluginName().data(), device_info.id.c_str());
    bool success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result_video.ToString());
            if (output_json.empty())
                break;
            if (!output_json.contains("video"))
                break;
            nlohmann::json json_video = output_json["video"];
            success = true;
            for (const auto& element : json_video)
            {
                VIDEO_CAPTURE_FORMAT fmt{};
                bool                 ok = false;
                fmt.format = MapVideoFormat2Native(static_cast<mediasdk::PixelFormat>(element["format"]), &ok);
                if (!ok)
                {
                    LOG(ERROR) << "mediasdk::PixelFormat transfer to VideoPixelFormatEnum failed! element[\"format\"] = " << element["format"];
                    continue;
                }
                fmt.width = element["size_x"];
                fmt.height = element["size_y"];
                fmt.rate = element["rate"];
                fmt.minRate = element["min_rate"];
                fmt.maxRate = element["max_rate"];
                video_format_list->push_back(fmt);
            }

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse EnumDeviceSupportFormats output error!";
    }
    if (audio_format_list)
    {
        if (!EnumDeviceSupportFormatsByDeviceName(device_info, audio_format_list))
            success = false;
    }
    return success;
}

bool MediaSDKControllerImplV2::EnumDeviceSupportFormatsByDeviceName(const DSHOW& device_info, std::vector<AUDIO_CAPTURE_FORMAT>* audio_format_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (!audio_format_list)
    {
        return false;
    }
    audio_format_list->clear();
    auto result_audio = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumAudioFormat, dshow_visual_source::GetPluginName().data(), device_info.id.c_str());
    bool success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result_audio.ToString());
            if (output_json.empty())
                break;
            if (!output_json.contains("audio"))
                break;
            nlohmann::json json_audio = output_json["audio"];
            success = true;
            for (const auto& element : json_audio)
            {
                AUDIO_CAPTURE_FORMAT fmt{};
                fmt.bitsPerSample = element.value("bits_per_sample", 0);
                fmt.channels = element.value("channels", 0.0f);
                fmt.sampleRate = element.value("sample_rate", 0.0f);
                audio_format_list->push_back(fmt);
            }

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse EnumDeviceSupportFormatsByDeviceName output error!";
    }
    return success;
}

bool MediaSDKControllerImplV2::CreateCameraSource(const std::string visual_id, const CAMERA_SOURCE& camera, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)

    if (InDshowCreateBlackList(camera.dshow.id.c_str()))
        return false;

    AddSourceInfo(visual_id, false);
    dshow_visual_source::CaptureParams vparam;
    bool                               ok = false;
    vparam.format = MapVideoFormat2SDK(camera.captureFormat.format, &ok);
    if (!ok)
    {
        LOG(ERROR) << "VideoPixelFormatEnum transfer to mediasdk::PixelFormat failed! param.captureFormat.format = " << static_cast<uint32_t>(camera.captureFormat.format);
        return false;
    }
    {
        bool ok = false;
        auto color_space = camera.colorConfig.colorSpace == COLOR_SPACE::COLOR_SPACE_UNSPECIFIED ? COLOR_SPACE::COLOR_SPACE_BT709 : camera.colorConfig.colorSpace;
        vparam.color_space = MapColorSpace2SDK(color_space, &ok);
        if (!ok)
        {
            LOG(ERROR) << "ColorSpaceEnum transfer to mediasdk::ColorSpace failed! vparam.color_space = " << static_cast<uint32_t>(vparam.color_space);
            return false;
        }
        auto video_range = camera.colorConfig.colorRange == COLOR_RANGE::COLOR_RANGE_UNSPECIFIED ? COLOR_RANGE::COLOR_RANGE_PARTIAL : camera.colorConfig.colorRange;
        vparam.video_range = MapColorRange2SDK(video_range, &ok);
        if (!ok)
        {
            LOG(ERROR) << "ColorRangeEnum transfer to mediasdk::VideoRange failed! vparam.video_range = " << static_cast<uint32_t>(vparam.video_range);
            return false;
        }
    }
    vparam.size = { static_cast<int32_t>(camera.captureFormat.width), static_cast<int32_t>(camera.captureFormat.height) };
    vparam.rate = camera.captureFormat.rate;
    vparam.min_rate = camera.captureFormat.minRate;
    vparam.max_rate = camera.captureFormat.maxRate;
    vparam.device_id = camera.dshow.id;
    vparam.device_name = camera.dshow.name;
    vparam.limit_rate = camera.maxRate;

    mediasdk::CreateVisualParams visual_params{};
    visual_params.plugin_name = dshow_visual_source::GetPluginName();
    {
        nlohmann::json json_root;
        json_root["type"] = dshow_visual_source::kCaptureTypeCamera;
        json_root["video"] = dshow_visual_source::CaptureParamsToJson(vparam);
        visual_params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
        visual_params.destroy_when_all_ref_removed = true;
    }
    visual_params.audio_track_id = 0;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), visual_params);
    if (success && result)
    {
        result->type = VISUAL_CAMERA;
        CAMERA_FILTER_STATE state = CAMERA_FILTER_STATE_STOPPED;
        bool camera_is_ready = false;
        success = CameraSourceGetState(visual_id, &camera_is_ready, &state, &result->createStepState);
        if (!success)
        {
            LOG(ERROR) << "[MediaSDKControllerImplV2::CreateCameraSource] CameraSourceGetState error!";
            return false;
        }
        result->reason = camera_is_ready ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_START_FAIL;

        if (result->createStepState.step == CAMERA_STEP_STATE::CAMERA_STEP_STATE_CONTEXT && result->createStepState.error == E_PENDING)
        {
            result->reason = CREATE_SOURCE_FAILED_REASON::CREATE_PENDING;
        }
        result->errorCode = success ? 0 : 1;
    }
    else
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateCameraSource] CreateVisual failed";
        return false;
    }
    return success;
}

bool MediaSDKControllerImplV2::ReopenCameraSourceWithTransform(const std::string& layer_id, const std::string& source_id, const CAMERA_SOURCE& camera, const TRANSFORM& transform, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    {
        nlohmann::json json_video;
        json_video["device_id"] = camera.dshow.id.c_str();
        json_video["device_name"] = camera.dshow.name.c_str();
        bool ok = false;
        json_video["format"] = MapVideoFormat2SDK(camera.captureFormat.format, &ok);
        if (!ok)
        {
            LOG(ERROR) << "VideoPixelFormatEnum transfer to mediasdk::PixelFormat failed! param.captureFormat.format = " << static_cast<uint32_t>(camera.captureFormat.format);
            return false;
        }
        json_video["size_cx"] = camera.captureFormat.width;
        json_video["size_cy"] = camera.captureFormat.height;
        json_video["rate"] = camera.captureFormat.rate;
        json_video["min_rate"] = camera.captureFormat.minRate;
        json_video["max_rate"] = camera.captureFormat.maxRate;
        json_video["limit_rate"] = camera.maxRate;
        {
            bool ok = false;
            auto color_space = camera.colorConfig.colorSpace == COLOR_SPACE::COLOR_SPACE_UNSPECIFIED ? COLOR_SPACE::COLOR_SPACE_BT709 : camera.colorConfig.colorSpace;
            json_video["default_color_space"] = MapColorSpace2SDK(color_space, &ok);
            if (!ok)
            {
                LOG(ERROR) << "ColorSpaceEnum transfer to mediasdk::ColorSpace failed! vparam.color_space = " << static_cast<uint32_t>(camera.colorConfig.colorSpace);
                return false;
            }
            auto video_range = camera.colorConfig.colorRange == COLOR_RANGE::COLOR_RANGE_UNSPECIFIED ? COLOR_RANGE::COLOR_RANGE_PARTIAL : camera.colorConfig.colorRange;
            json_video["default_video_range"] = MapColorRange2SDK(video_range, &ok);
            if (!ok)
            {
                LOG(ERROR) << "ColorRangeEnum transfer to mediasdk::VideoRange failed! vparam.video_range = " << static_cast<uint32_t>(camera.colorConfig.colorRange);
                return false;
            }
        }
        json_root["video"] = json_video;
    }

    mediasdk::ReopenParam reopen_param{};
    reopen_param.canvas_item_id = layer_id;
    reopen_param.target_transform = MapTransform2SDK(transform);
    std::vector<mediasdk::ReopenParam> vec_params{};
    vec_params.push_back(reopen_param);
    mediasdk::MediaSDKArray reopen_params(vec_params);
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.ReopenVisualAndUpdateTransforms, source_id.c_str(), json_root.dump().c_str(), reopen_params);
    if (result)
    {
        result->type = VISUAL_CAMERA;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Reopen CameraSource error!";
        return false;
    }
    if (result)
    {
        result->type = VISUAL_CAMERA;
        CAMERA_FILTER_STATE state = CAMERA_FILTER_STATE_STOPPED;
        bool camera_is_ready = false;
        success = CameraSourceGetState(source_id, &camera_is_ready, &state, &result->createStepState);
        if (!success)
        {
            LOG(ERROR) << "[ReopenCameraSource]CameraSourceGetState error!";
            return false;
        }
        result->reason = camera_is_ready ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_START_FAIL;
    }
    return success;
}

bool MediaSDKControllerImplV2::CameraSourceSetPrepareTransform(const std::string& visual_id, const TRANSFORM& transform_info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSClipF clip = {transform_info.clipRange.x, transform_info.clipRange.y, transform_info.clipRange.z, transform_info.clipRange.w};

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemSourceClip, visual_id.c_str(), clip);
    return success;
}

bool MediaSDKControllerImplV2::CameraSourceGetState(const std::string& visual_id, bool* camera_is_ready, CAMERA_FILTER_STATE* state, CAMERA_CREATE_STEP_STATE* create_step_state)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::CameraSourceGetState)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), "camera_state");
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty() && output_json.contains("camera_state"))
                break;
            if (state)
                *state = output_json["camera_state"];

            success = true;
        } while (0);

        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty() && !output_json.contains("camera_is_ready"))
                break;
            if (camera_is_ready)
                *camera_is_ready = output_json["camera_is_ready"];

            success = true;
        } while (0);

        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            if (create_step_state)
            {
                if (output_json.contains("camera_create_state"))
                {
                    nlohmann::json create_state_json = output_json["camera_create_state"];
                    if (create_state_json.empty())
                        break;
                    if (create_state_json.contains("step"))
                        create_step_state->step = create_state_json["step"];
                    if (create_state_json.contains("error"))
                        create_step_state->error = create_state_json["error"];
                }
                else
                {
                    create_step_state->step = CAMERA_STEP_STATE::CAMERA_STEP_STATE_START;
                    create_step_state->error = NOERROR;
                }
                success = true;
            }
        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse CameraSourceGetState output error!";
    }

    return success;
}

bool MediaSDKControllerImplV2::CameraSourceSetAmpProperty(const std::string& visual_id, int64_t win_id, VIDEO_PROCAMP_TYPE key, int32_t value, int32_t flag)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    bool           ok = false;
    json_root["property_index"] = MapVideoProcAmpType2SDK(key, &ok);
    if (!ok)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CameraSourceSetAmpProperty] MapVideoProcAmpType2SDK failed";
        return false;
    }
    json_root["flag"] = flag;
    json_root["value"] = value;
    std::string json_params = json_root.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), "camera_video_proc_amp", json_params.c_str());
    return success;
}

bool MediaSDKControllerImplV2::CameraSourceGetAmpProperty(const std::string& visual_id, int64_t win_id, std::vector<VIDEO_PROC_AMP>* properties)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), "camera_video_proc_amp");
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            nlohmann::json control_json = output_json["camera_video_proc_amp"];
            if (properties)
            {
                properties->clear();
                for (auto it : control_json)
                {
                    VIDEO_PROC_AMP out;
                    int index = it["property_index"];
                    bool ok = false;
                    out.type = MapVideoProcAmpType2Native(index, &ok);
                    if (!ok)
                    {
                        LOG(ERROR) << "[MediaSDKControllerImplV2::CameraSourceGetAmpProperty] MapVideoProcAmpType2Native failed";
                        continue;
                    }
                    out.attr.min = it["min_value"];
                    out.attr.max = it["max_value"];
                    out.attr.step = it["step"];
                    out.attr.defVal = it["default_value"];
                    out.attr.flag = it["default_flag"];
                    out.attr.value = it["value"];
                    out.attr.currentFlag = it["flag"];
                    properties->push_back(out);
                }
            }
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse CameraSourceGetAmpProperty output error!";
    }

    return success;
}

bool MediaSDKControllerImplV2::CameraSourceSetControlProperty(const std::string& visual_id, int64_t win_id, CAMERA_CONTROL_TYPE key, int32_t value, int32_t flag)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    bool ok = false;
    json_root["property_index"] = MapCameraControlType2SDK(key, &ok);
    if (!ok)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CameraSourceSetControlProperty] MapCameraControlType2SDK failed";
        return false;
    }
    json_root["flag"] = flag;
    json_root["value"] = value;
    std::string json_params = json_root.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), "camera_video_control", json_params.c_str());
    return success;
}

bool MediaSDKControllerImplV2::CameraSourceGetControlProperty(const std::string& visual_id, int64_t win_id, std::vector<CAMERA_CONTROL>* properties)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), "camera_video_control");
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            nlohmann::json control_json = output_json["camera_video_control"];
            if (properties)
            {
                properties->clear();
                for (auto it : control_json)
                {
                    CAMERA_CONTROL out;
                    int index = it["property_index"];
                    bool ok = false;
                    out.type = MapCameraControlType2Native(index, &ok);
                    if (!ok)
                    {
                        LOG(ERROR) << "[MediaSDKControllerImplV2::CameraSourceGetControlProperty] MapCameraControlType2Native failed";
                        continue;
                    }
                    out.attr.min = it["min_value"];
                    out.attr.max = it["max_value"];
                    out.attr.step = it["step"];
                    out.attr.defVal = it["default_value"];
                    out.attr.flag = it["default_flag"];
                    out.attr.value = it["value"];
                    out.attr.currentFlag = it["flag"];
                    properties->push_back(out);
                }
            }
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse CameraSourceGetControlProperty output error!";
    }

    return success;
}

bool MediaSDKControllerImplV2::CameraSourceSetDetectColorRangeParam(const std::string& visual_id, uint32_t detect_interval_sec, uint32_t limited_detect_count)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["detect_interval"] = detect_interval_sec;
    json_root["limited_detect_count"] = limited_detect_count;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), dshow_visual_source::kDShowCameraStartDetectVideoRange, json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::CameraSourceStopDetectColorRange(const std::string& visual_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["stop_detect"] = true;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), dshow_visual_source::kDShowCameraStopDetectVideoRange, json_root.dump().c_str());

    return success;
}

bool MediaSDKControllerImplV2::CameraSourceSetMaxCaptureFps(const std::string& visual_id, const float max_fps)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["limit_rate"] = max_fps;
    std::string json_params = json_root.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), dshow_visual_source::kDShowCameraLimitRate, json_params.c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateCaptureCardSource(const std::string visual_id, const ANALOG_SOURCE& analog, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)

    if (InDshowCreateBlackList(analog.videoDevice.id.c_str()))
        return false;

    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = dshow_visual_source::GetPluginName();
    {
        nlohmann::json json_root;
        json_root["type"] = dshow_visual_source::kCaptureTypeAnalog;
        {
            nlohmann::json json_video;
            json_video["device_id"] = analog.videoDevice.id.c_str();
            json_video["device_name"] = analog.videoDevice.name.c_str();
            bool ok = false;
            json_video["format"] = MapVideoFormat2SDK(analog.videoCapFormat.format, &ok);
            if (!ok)
            {
                LOG(ERROR) << "VideoPixelFormatEnum transfer to mediasdk::PixelFormat failed! param.video_device_format.format = " << static_cast<uint32_t>(analog.videoCapFormat.format);
                return false;
            }
            json_video["size_cx"] = analog.videoCapFormat.width;
            json_video["size_cy"] = analog.videoCapFormat.height;
            json_video["rate"] = analog.videoCapFormat.rate;
            json_video["min_rate"] = analog.videoCapFormat.minRate;
            json_video["max_rate"] = analog.videoCapFormat.maxRate;
            json_video["default_color_space"] = MapColorSpace2SDK(analog.colorConfig.colorSpace, &ok);
            if (!ok)
            {
                LOG(ERROR) << "ColorSpaceEnum transfer to mediasdk::ColorSpace failed! param.color_space = " << static_cast<uint32_t>(analog.colorConfig.colorSpace);
                return false;
            }
            json_video["default_video_range"] = MapColorRange2SDK(analog.colorConfig.colorRange, &ok);
            if (!ok)
            {
                LOG(ERROR) << StringPrintf("ColorRangeEnum transfer to mediasdk::VideoRange failed! param.color_range = ") << static_cast<uint32_t>(analog.colorConfig.colorRange);
                return false;
            }
            json_root["video"] = json_video;
        }
        if (!analog.audioDevice.id.empty())
        {
            nlohmann::json json_audio;
            json_audio["device_id"] = analog.audioDevice.id.c_str();
            json_audio["device_name"] = analog.audioDevice.name.c_str();
            json_audio["channels"] = analog.audioCapFormat.channels;
            json_audio["sample_rate"] = analog.audioCapFormat.sampleRate;
            json_audio["bits_per_sample"] = analog.audioCapFormat.bitsPerSample;
            json_audio["render_type"] = 0;
            json_root["audio"] = json_audio;
            uint32_t track_ids = analog.audioTrack;
            bool     res = GetFirstAudioTrackOfTrackIds(track_ids, track_ids);
            if (!res)
            {
                LOG(ERROR) << "[MediaSDKControllerImplV2::CreateCaptureCardSource] GetFirstAudioTrackOfTrackIds error! analog.audioTrack = " << analog.audioTrack;
            }
            if (params.audio_track_id > 0)
            {
                audio_id_map_[visual_id] = 1 << (params.audio_track_id - 1);
            }
        }
        else
        {
            params.audio_track_id = 0;
        }
        params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
        params.destroy_when_all_ref_removed = true;
    }

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_ANALOG;
        CAMERA_FILTER_STATE state = CAMERA_FILTER_STATE_STOPPED;
        bool camera_is_ready = false;
		success = CameraSourceGetState(visual_id, &camera_is_ready, &state, &result->createStepState);
		if (!success)
		{
			LOG(ERROR) << "[MediaSDKControllerImplV2::CreateCaptureCardSource] CameraSourceGetState error!";
			return false;
		}
		result->reason = camera_is_ready ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_START_FAIL;

		if (result->createStepState.step == CAMERA_STEP_STATE::CAMERA_STEP_STATE_CONTEXT && result->createStepState.error == E_PENDING)
		{
			result->reason = CREATE_SOURCE_FAILED_REASON::CREATE_PENDING;
		}
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create CaptureCardSource error!";
        return false;
    }

    audio_track_not_remove_.insert(visual_id);
    return success;
}

bool MediaSDKControllerImplV2::ReopenAnalogSourceWithTransform(const std::string& layer_id, const std::string& source_id, const ANALOG_SOURCE& analog, const TRANSFORM& transform, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    {
        nlohmann::json json_video;
        json_video["device_id"] = analog.videoDevice.id.c_str();
        json_video["device_name"] = "";
        bool ok = false;
        json_video["format"] = MapVideoFormat2SDK(analog.videoCapFormat.format, &ok);
        if (!ok)
        {
            LOG(ERROR) << "VideoPixelFormatEnum transfer to mediasdk::PixelFormat failed! param.video_device_format.format = " << static_cast<uint32_t>(analog.videoCapFormat.format);
            return false;
        }
        json_video["size_cx"] = analog.videoCapFormat.width;
        json_video["size_cy"] = analog.videoCapFormat.height;
        json_video["rate"] = analog.videoCapFormat.rate;
        json_video["min_rate"] = analog.videoCapFormat.minRate;
        json_video["max_rate"] = analog.videoCapFormat.maxRate;
        json_video["color_space"] = MapColorSpace2SDK(analog.colorConfig.colorSpace, &ok);
        if (!ok)
        {
            LOG(ERROR) << "ColorSpaceEnum transfer to mediasdk::ColorSpace failed! param.color_space = " << static_cast<uint32_t>(analog.colorConfig.colorSpace);
            return false;
        }
        json_video["video_range"] = MapColorRange2SDK(analog.colorConfig.colorRange, &ok);
        if (!ok)
        {
            LOG(ERROR) << "ColorRangeEnum transfer to mediasdk::VideoRange failed! param.color_range = " << static_cast<uint32_t>(analog.colorConfig.colorRange);
            return false;
        }
        json_root["video"] = json_video;
    }
    if (!analog.audioDevice.id.empty())
    {
        nlohmann::json json_audio;
        json_audio["device_id"] = analog.audioDevice.id.c_str();
        json_audio["device_name"] = analog.audioDevice.name.c_str();
        json_audio["channels"] = analog.audioCapFormat.channels;
        json_audio["sample_rate"] = analog.audioCapFormat.sampleRate;
        json_audio["bits_per_sample"] = analog.audioCapFormat.bitsPerSample;
        json_audio["render_type"] = 0;
        json_root["audio"] = json_audio;
    }

    mediasdk::ReopenParam reopen_param{};
    reopen_param.canvas_item_id = layer_id;
    reopen_param.target_transform = MapTransform2SDK(transform);
    std::vector<mediasdk::ReopenParam> vec_params{};
    vec_params.push_back(reopen_param);
    mediasdk::MediaSDKArray reopen_params(vec_params);
    bool                    success = call_helper_.SyncCall<bool>(g_sdk_api.ReopenVisualAndUpdateTransforms, source_id.c_str(), json_root.dump().c_str(), reopen_params);
    if (result)
    {
        result->type = VISUAL_ANALOG;
        CAMERA_FILTER_STATE state = CAMERA_FILTER_STATE_STOPPED;
        bool camera_is_ready = false;
        success = CameraSourceGetState(source_id, &camera_is_ready, &state, &result->createStepState);
        if (!success)
        {
            LOG(ERROR) << "[ReopenAnalogSourceWithTransform] CameraSourceGetState error!";
            return false;
        }
        result->reason = camera_is_ready ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_START_FAIL;

        if (result->createStepState.step == CAMERA_STEP_STATE::CAMERA_STEP_STATE_CONTEXT && result->createStepState.error == E_PENDING)
        {
            result->reason = CREATE_SOURCE_FAILED_REASON::CREATE_PENDING;
        }
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create CaptureCardSource error!";
        return false;
    }

    audio_track_not_remove_.insert(source_id);
    return success;
}

bool MediaSDKControllerImplV2::CreateGraffitiSource(const std::string visual_id, const GRAFFITI_SOURCE& graffiti, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::CreateVisualParams params{};
    params.plugin_name = graffiti_visual_source::GetPluginName();
    params.audio_track_id = 0;
    params.destroy_when_all_ref_removed = true;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_GRAFFITI;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateGraffitiSource] CreateVisual error!";
    }
    return success;
}

bool MediaSDKControllerImplV2::SetLayerNeedDrawBorder(const std::string& layer_id, bool need_draw_border)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemNeedDrawBorder, layer_id.c_str(), need_draw_border);
	if (!success)
	{
		LOG(ERROR) << "[MediaSDKControllerImplV2::GraffitiSourceSetEditState] SetCanvasItemNeedDrawBorder error!";
	}
	return success;
}

bool MediaSDKControllerImplV2::GraffitiSourceSetEditState(const std::string& visual_id, const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemEditable, visual_id.c_str(), enable);
	if (!success)
	{
		LOG(ERROR) << "[MediaSDKControllerImplV2::GraffitiSourceSetEditState] SetCanvasItemEditable error!";
	}
    return success;
}

bool MediaSDKControllerImplV2::GraffitiSourceGetEditState(const std::string& visual_id, bool* enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::ResultBoolBool ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.GetCanvasItemEditable, visual_id.c_str());
    if (ret.success)
    {
        if (enable)
            *enable = ret.value;
    }
    return ret.success;
}

bool MediaSDKControllerImplV2::GraffitiSourceSetType(const std::string& visual_id, const GRAFFITI_TYPE type)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["shape"] = MapGraffitiType2SDK(type);
    std::string json_params = json_root.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), "current_graffiti_shape", json_params.c_str());
    return success;
}

bool MediaSDKControllerImplV2::GraffitiSourceSetPen(const std::string& visual_id, const int32_t r, const int32_t g, const int32_t b, const float a, const float width)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    uint32_t       pen_color = (0xFF << 24) | (r << 16) | (g << 8) | b;
    json_root["color"] = pen_color;
    std::string json_params = json_root.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), "graffiti_pen_color", json_params.c_str());
    if (!success)
        return false;
    json_root["width"] = width * 10;
    json_params = json_root.dump();
    success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualInputProperty, visual_id.c_str(), "graffiti_pen_width", json_params.c_str());
    return success;
}

bool MediaSDKControllerImplV2::GraffitiSourceRemoveAll(const std::string& visual_id, const bool restore)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["action"] = "erase_all";
    std::string json_params = json_root.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json_params.c_str());
    return success;
}

bool MediaSDKControllerImplV2::GraffitiSourceRemove(const std::string& visual_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["action"] = "undo";
    std::string json_params = json_root.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json_params.c_str());
    return success;
}

bool MediaSDKControllerImplV2::GraffitiSourceRecover(const std::string& visual_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["action"] = "redo";
    std::string json_params = json_root.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.DoVisualInputAction, visual_id.c_str(), json_params.c_str());
    return success;
}

bool MediaSDKControllerImplV2::GraffitiSourceHasTracks(const std::string& visual_id, bool* value)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::GraffitiSourceHasTracks)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), "can_undo_graffiti");
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            if (!output_json.contains("can_undo_graffiti"))
            {
                break;
            }
            if (value)
                *value = output_json["can_undo_graffiti"];
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse GraffitiSourceHasTracks output error!";
    }
    return success;
}

bool MediaSDKControllerImplV2::GraffitiSourceHasRestores(const std::string& visual_id, bool* value)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualInputProperty, visual_id.c_str(), "can_redo_graffiti");
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            if (!output_json.contains("can_redo_graffiti"))
            {
                break;
            }
            if (value)
                *value = output_json["can_redo_graffiti"];
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse GraffitiSourceHasRestores output error!";
    }
    return success;
}

static inline nlohmann::json MobileProjectorOptions(const std::vector<BYTELINK_OPTION>& options, bool* success)
{
    nlohmann::json json_root;
    bool           all_ok = true;
    for (const auto it : options)
    {
        const auto  type = it.castmateOptionType;
        const auto& opt_cfg_str = it.config;

        bool ok = false;
        auto opt_type = MapCastMateOptionType2SDK(type, &ok);
        if (!ok)
        {
            all_ok = false;
            break;
        }
        nlohmann::json json_item;
        json_item["type"] = opt_type;
        json_item["config"] = opt_cfg_str;
        json_root.push_back(json_item);
    }
    if (success)
        *success = all_ok;
    return json_root;
}

bool MediaSDKControllerImplV2::CreateMobileProjectorSource(const std::string visual_id, const BYTELINK_SOURCE& bytelink, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = bytelink_visual_source::GetPluginName();
    uint32_t track_ids = bytelink.audioTrack;
    params.audio_track_id = 0;
    bool res = GetFirstAudioTrackOfTrackIds(track_ids, params.audio_track_id);
    if (!res)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateMobileProjectorSource] GetFirstAudioTrackOfTrackIds error! bytelink.audioTrack = " << bytelink.audioTrack;
    }
    if (params.audio_track_id > 0)
    {
        audio_id_map_[visual_id] = 1 << (params.audio_track_id - 1);
    }
    nlohmann::json json_root, json_options;
    bool           ok = false;
    json_root["type"] = MapCastmateProtocolType2SDK(bytelink.protocolType, &ok);
    if (!ok)
    {
        LOG(ERROR) << StringPrintf("MobileProjectorProtocolEnum transfer to bytelink_visual_source::CastmateProtocolType failed! bytelink.protocolType = ") << static_cast<uint32_t>(bytelink.protocolType);
        return false;
    }
    json_root["loacl_port"] = 3230;
    json_root["fps"] = bytelink.fps;
    if (bytelink.protocolType == CASTMATE_PROTOCOL_TYPE::WIRED_ANDROID ||
        bytelink.protocolType == CASTMATE_PROTOCOL_TYPE::WIRED_IOS ||
        bytelink.protocolType == CASTMATE_PROTOCOL_TYPE::WIRELESS_ANDROID)
    {
        json_root["width"] = 0;
        json_root["heigth"] = 0;
    }
    else
    {
        json_root["width"] = 1920;
        json_root["heigth"] = 1080;
    }
    json_root["bitrate"] = 7000;
    json_root["name"] = bytelink.name.c_str();
    ILSPlatform* LSPlatform = (ILSPlatform*)g_cief->QueryInterface(LSINAME_LSPLATFORM);
    if (!LSPlatform)
        return false;
    json_root["device_id"] = LSPlatform->GetDeviceID();
    json_root["user_id"] = LSPlatform->GetUserID();
    json_root["background_image"] = bytelink.closePic.c_str();
    json_root["options"] = MobileProjectorOptions(bytelink.bytelinkOptions, &ok);
    json_root["decode_packet_format"] = mediasdk::PixelFormat::kPixelFormatNV12;
    json_root["decode_packet_memory_type"] = bytelink_visual_source::CastMateFrameFromat::kCastMateFrameFromatBuffer;
    if (!ok)
        return false;
    params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
    params.destroy_when_all_ref_removed = true;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params);
    if (result)
    {
        result->type = VISUAL_BYTELINK;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "Create MobileProjectorSource error!";
        return false;
    }
    audio_track_not_remove_.insert(visual_id);
    
    sdk_callback_mgr_->AddMobileProjectorID(visual_id);
    return success;
}

bool MediaSDKControllerImplV2::MobileProjectorSourceSetOption(const std::vector<BYTELINK_OPTION>& options)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool           ok = false;
    nlohmann::json json_root = MobileProjectorOptions(options, &ok);
    if (!ok)
        return false;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualSourceProperty, bytelink_visual_source::GetPluginName().data(), bytelink_visual_source::kBytelinkSetOptions, json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::MobileProjectorSourceCheckBonjour(bool* value)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["key"] = bytelink_visual_source::kBytelinkBonjourCheck;
    ILSPlatform* lsPlatform = (ILSPlatform*)g_cief->QueryInterface(LSINAME_LSPLATFORM);
    if (!lsPlatform)
        return false;
    json_root["device_id"] = lsPlatform->GetDeviceID();
    json_root["user_id"] = lsPlatform->GetUserID();
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVisualSourceProperty,
                                                                                      bytelink_visual_source::GetPluginName().data(), json_root.dump().c_str());
    bool                     success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;

            if (value)
            {
                *value = output_json["installed"];
            }
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse MobileProjectorSourceCheckBonjour output error!";
    }
    return success;
}

bool MediaSDKControllerImplV2::CreateRTCSource(const std::string visual_id, const RTC_SOURCE& rtc, VISUAL_SOURCE_RESULT* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    AddSourceInfo(visual_id, false);
    mediasdk::CreateVisualParams params{};
    params.plugin_name = rtc_visual_source::GetPluginName();
    {
        nlohmann::json json_root;
        json_root["user_id"] = rtc.uid;
        json_root["stream_index"] = rtc.streamType == RTC_STREAM_TYPE::VIDEO_STREAM ? mediasdk::StreamIndex::kStreamIndexMain : mediasdk::StreamIndex::kStreamIndexScreen;
        params.json_params = json_root.dump();
        params.destroy_when_all_ref_removed = true;
    }
    params.audio_track_id = rtc.audioTrack;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisual, visual_id.c_str(), params); // rtc的源创建和音频创建分开
    if (result)
    {
        result->type = VISUAL_RTC;
        result->reason = success ? CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL : CREATE_SOURCE_FAILED_REASON::CREATE_INIT_FAIL;
        result->errorCode = success ? 0 : 1;
    }
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateRTCSource] Create RTCSource error!";
        return false;
    }
    {
        std::unique_lock<std::mutex> lock(g_mutex_);
        g_rtc_visual_ids.insert(visual_id);
    }

    mediasdk::CreateAudioParams pp;
    pp.track_id = 0;
    if (pp.track_id > 0)
    {
        audio_id_map_[visual_id] = 1 << (pp.track_id - 1);
    }
    pp.plugin_name = mediasdk::MediaSDKString("RTCAudioInputSource");
    {
        nlohmann::json json_root;
        json_root["user_id"] = rtc.uid;
        json_root["stream_index"] = rtc.streamType == RTC_STREAM_TYPE::VIDEO_STREAM ? mediasdk::StreamIndex::kStreamIndexMain : mediasdk::StreamIndex::kStreamIndexScreen;
        pp.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
    }

    success = call_helper_.SyncCall<bool>(g_sdk_api.CreateAudioInput, visual_id.c_str(), pp);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateRTCSource] CreateAudioInput error! audio_id = " << rtc.audioTrack;
        return false;
    }
    success = AudioSourceSetTracks(visual_id, rtc.audioTrack);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateRTCSource] AudioSourceSetTracks error! audio_id = " << rtc.audioTrack;
    }
    return success;
}

bool MediaSDKControllerImplV2::CreateVirtualCameraLayer(const std::string& canvas_item_id, const VIRTUAL_CAMERA_SOURCE& virtual_camera)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSSize size{};
    size.cx = virtual_camera.outputSize.Width;
    size.cy = virtual_camera.outputSize.Height;

    bool ok = false;
    mediasdk::VirtualCameraObjectFitMode fit_mode = MapObjectFitMode2SDK(virtual_camera.fitMode, &ok);
    if (!ok)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateVirtualCameraSource] MapObjectFitMode2SDK failed";
        return false;
    }

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.StartVirtualCamera, virtual_camera.refID.c_str(), size, virtual_camera.bkColor, fit_mode);
    if (!success)
    {
        LOG(ERROR) << "[CreateVirtualCameraSource] StartVirtualCamera error!";
        return false;
    }

    virtual_camera_id_ = canvas_item_id;
    sdk_callback_mgr_->SetVirtualCameraID(canvas_item_id);
    return success;
}

bool MediaSDKControllerImplV2::VirtualCameraSourceReopen(const std::string& ref_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SwitchVirtualCamera, ref_id.c_str());
    if (!success)
    {
        LOG(ERROR) << "[VirtualCameraSourceReopen] SwitchVirtualCamera error!";
    }

    success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemPreprocess, ref_id.c_str(), false);
    if (!success)
    {
        LOG(ERROR) << "[VirtualCameraSourceReopen] SetVisualPreprocess error!";
        return false;
    }

    return success;
}

bool MediaSDKControllerImplV2::VisualDestroy(const std::string& visual_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    if (visual_id == virtual_camera_id_)
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.StopVirtualCamera);
        if (!success)
        {
            LOG(WARNING) << "StopVirtualCamera error !";
        }
        return success;
    }

    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyVisual, visual_id.c_str());
        if (!success)
        {
            LOG(WARNING) << "[MediaSDKControllerImplV2::VisualDestroy] DestroyVisual error, id = " << visual_id;
        }

        {
            if (g_rtc_visual_ids.count(visual_id))
            {
                success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyAudioInput, visual_id.c_str());
                if (!success)
                {
                    LOG(WARNING) << "[MediaSDKControllerImplV2::VisualDestroy] DestroyAudioInput error, id = " << visual_id;
                }
                {
                    std::unique_lock<std::mutex> lock(g_mutex_);
                    g_rtc_visual_ids.erase(visual_id);
                }
            }
        }

        if (rtc_not_output_visual_ids_.count(visual_id))
        {
            rtc_not_output_visual_ids_.erase(visual_id);
        }
        if (rtc_not_output_screen_visual_ids_.count(visual_id))
        {
            rtc_not_output_screen_visual_ids_.erase(visual_id);
        }
    }
    if (success)
    {
        {
            std::unique_lock<std::mutex> lock(game_source_info_mutex_);
            game_source_info_.erase(visual_id);
        }
        if (audio_id_map_.count(visual_id))
        {
            audio_id_map_.erase(visual_id);
        }
        RemoveSourceInfo(visual_id);

        sdk_callback_mgr_->EraseMobileProjectorID(visual_id);
        size_t count = 0;
        {
            std::unique_lock<std::mutex> lock(visual_info_mutex_);
            count = visual_info_.erase(visual_id);
        }
    }
    return success;
}

bool MediaSDKControllerImplV2::LayerSetVisible(const std::string& layer_id, const bool is_visible)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemVisible, layer_id.c_str(), is_visible);
    return success;
}

bool MediaSDKControllerImplV2::VisualSaveAsImage(const std::string& visual_id, const std::string& filepath)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CanvasItemSaveAsFile, visual_id.c_str(), filepath.c_str(), mediasdk::ImageFileFormat::kImageFileFormatPNG);
    return success;
}

bool MediaSDKControllerImplV2::LayerSetFlag(const std::string& layer_id, VISUAL_FLAG flag)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    if (flag == VISUAL_FLAG::VISUAL_FLAG_ALWAYS_TOP)
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemAlwaysTop, layer_id.c_str(), true);
    }
    else if (flag == VISUAL_FLAG::VISUAL_FLAG_OUTPUT_FILTER)
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemAvoidOutput, layer_id.c_str(), true);
    }
    else if (flag == VISUAL_FLAG::VISUAL_FLAG_RTC_NOT_OUTPUT)
    {
        nlohmann::json json_root;
        nlohmann::json json_visual_ids;
        rtc_not_output_visual_ids_.insert(layer_id);
        for (auto it : rtc_not_output_visual_ids_)
        {
            json_visual_ids.push_back(it);
        }
        json_root["visual_ids"] = json_visual_ids;
        int ret = call_helper_.SyncCall<int>(g_sdk_api.UpdateExcludedVisuals, mediasdk::StreamIndex::kStreamIndexMain, json_root.dump().c_str());
        if (ret != 0)
        {
            LOG(ERROR) << "kStreamIndexMain UpdateExcludedVisuals error! ret = " << ret;
            success = false;
        }
        else
        {
            success = true;
        }
    }
    else if (flag == VISUAL_FLAG::VISUAL_FLAG_RTC_NOT_OUTPUT_TO_SCREEN)
    {
        nlohmann::json json_root;
        nlohmann::json json_visual_ids;
        rtc_not_output_screen_visual_ids_.insert(layer_id);
        for (auto it : rtc_not_output_screen_visual_ids_)
        {
            json_visual_ids.push_back(it);
        }
        json_root["visual_ids"] = json_visual_ids;
        int ret = call_helper_.SyncCall<int>(g_sdk_api.UpdateExcludedVisuals, mediasdk::StreamIndex::kStreamIndexScreen, json_root.dump().c_str());
        if (ret != 0)
        {
            LOG(ERROR) << "kStreamIndexScreen UpdateExcludedVisuals error! ret = " << ret;
            success = false;
        }
        else
        {
            success = true;
        }
    }
    else if (flag == VISUAL_FLAG::VISUAL_FLAG_RTC_ADD_TO_OUTPUT)
    {
        if (rtc_not_output_visual_ids_.count(layer_id))
        {
            rtc_not_output_visual_ids_.erase(layer_id);
        }
        success = true;
    }
    else if (flag == VISUAL_FLAG::VISUAL_FLAG_RTC_ADD_TO_OUTPUT_SCREEN)
    {
        if (rtc_not_output_screen_visual_ids_.count(layer_id))
        {
            rtc_not_output_screen_visual_ids_.erase(layer_id);
        }
        success = true;
    }
    return success;
}

bool MediaSDKControllerImplV2::VisualCapturePause(const std::string& visual_id, bool pause)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;

    if (pause)
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.PauseVisualCapture, visual_id.c_str());
    }
    else
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.ContinueVisualCapture, visual_id.c_str());
    }
    return success;
}

bool MediaSDKControllerImplV2::LayerSetRotate(const std::string& layer_id, const float angle)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemRotate, layer_id.c_str(), angle);
    return success;
}

bool MediaSDKControllerImplV2::VisualGetRotate(const std::string& visual_id, float* angle)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolFloat>(g_sdk_api.GetCanvasItemRotate, visual_id.c_str());
    if (ret.success && angle)
        *angle = ret.value;
    return ret.success;
}

bool MediaSDKControllerImplV2::LayerSetVerticalFlip(const std::string& layer_id, const bool flip)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemFlipV, layer_id.c_str(), flip);
    return success;
}

bool MediaSDKControllerImplV2::VisualGetVerticalFlip(const std::string& visual_id, bool* flip)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool val = call_helper_.SyncCall<bool>(g_sdk_api.GetCanvasItemFlipV, visual_id.c_str());
    if (flip)
        *flip = val;
    return true;
}

bool MediaSDKControllerImplV2::LayerSetHorizontalFlip(const std::string& layer_id, const bool flip)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemFlipH, layer_id.c_str(), flip);
    return success;
}

bool MediaSDKControllerImplV2::LayerGetHorizontalFlip(const std::string& layer_id, bool* flip)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool val = call_helper_.SyncCall<bool>(g_sdk_api.GetCanvasItemFlipH, layer_id.c_str());
    if (flip)
        *flip = val;
    return true;
}

bool MediaSDKControllerImplV2::GetSourceFPS(const std::string& source_id, float* fps)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::GetSourceFPS)
    float retFps;
    bool  ret = g_sdk_api.GetVisualFPS(source_id.c_str(), retFps);
    if (fps)
    {
        *fps = ret ? retFps : 0;
    }
    return true;
}

bool MediaSDKControllerImplV2::VisualGetEffectProfiler(const std::string& layer_id, EffectProfilerInfo* effect_profiler_info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::VisualGetEffectProfiler)
    bool success = false;
    auto filterId = layer_id + "_filter";

    bool        call_success = false;
    const char* fail_reason = "";
    do
    {

        mediasdk::MediaSDKString retFilter;
        call_helper_.SyncCallWithTimeout<mediasdk::MediaSDKString>(kV2CallTimeoutMS, call_success, retFilter, g_sdk_api.VisualFilterAction, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kActionGetEffectFps, nullptr);
        if (!call_success)
        {
            fail_reason = "VisualFilterAction kActionGetEffectFps timeout!";
            break;
        }

        try
        {
            nlohmann::json json_root_filter = nlohmann::json::parse(retFilter.ToString());
            if (effect_profiler_info)
            {
                if (json_root_filter.contains("inEffectFps"))
				    effect_profiler_info->before_effect_fps = static_cast<int32_t>(json_root_filter["inEffectFps"]);
                if (json_root_filter.contains("effectFps"))
				    effect_profiler_info->after_effect_fps = static_cast<int32_t>(json_root_filter["effectFps"]);
                if (json_root_filter.contains("effectAchieveRate"))
				    effect_profiler_info->effect_achieve_rate = static_cast<float>(json_root_filter["effectAchieveRate"]);
                if (json_root_filter.contains("effectTickAchieveRate"))
				    effect_profiler_info->tick_achieve_rate = static_cast<float>(json_root_filter["effectTickAchieveRate"]);
            }
        }
        catch (...)
        {
            fail_reason = "parse kActionGetEffectFps json error!";
            break;
        }

        success = true;
    } while (0);

    if (!success)
    {
        LOG(ERROR) << fail_reason;
    }
    return success;
}

bool MediaSDKControllerImplV2::VisualSetBackground(const std::string& visual_id, const std::string& file, bool point9, int lt, int lb, int tl, int tr, float scale)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    nlohmann::json json_border;
    json_border["left_start"] = lt;
    json_border["left_end"] = lb;
    json_border["top_start"] = tl;
    json_border["top_end"] = tr;
    json_root["point_nine_border"] = json_border;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualOverlay, visual_id.c_str(), file.c_str(), scale, json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::VisualAttach(const std::string& visual_id, const int32_t track_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    {
        std::unique_lock<std::mutex> lock(visual_info_mutex_);
        auto&                        info = visual_info_[visual_id];
        info.visual_id = visual_id;
        info.detach = false;
        info.track_id = track_id;
    }

    call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemVisible, visual_id.c_str(), true);
    return true;
}

bool MediaSDKControllerImplV2::VisualDetach(const std::string& visual_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool    success = false;
    int32_t track_id = 0;
    {
        std::unique_lock<std::mutex> lock(visual_info_mutex_);

        auto found = visual_info_.find(visual_id);
        if (found != visual_info_.end())
        {
            track_id = found->second.track_id;
            success = true;
        }
    }
    if (!success)
    {
        return false;
    }

    call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemVisible, visual_id.c_str(), false);
    return true;
}

bool MediaSDKControllerImplV2::LayerSetClip(const std::string& layer_id, float x, float y, float z, float w)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSClipF clip;
    clip.x = x;
    clip.y = y;
    clip.w = w;
    clip.z = z;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemClip, layer_id.c_str(), clip);
    return success;
}

bool MediaSDKControllerImplV2::VisualGetClip(const std::string& visual_id, float* x, float* y, float* z, float* w)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolMSClipF>(g_sdk_api.GetCanvasItemClip, visual_id.c_str());
    if (!ret.success)
        return false;

    if (x)
        *x = ret.value.x;
    if (y)
        *y = ret.value.y;
    if (z)
        *z = ret.value.z;
    if (w)
        *w = ret.value.w;
    return true;
}

bool MediaSDKControllerImplV2::VisualSetEnableClip(const std::string& visual_id, const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.EnableCanvasItemClip, visual_id.c_str(), enable);
    return success;
}

bool MediaSDKControllerImplV2::VisualGetEnableClip(const std::string& visual_id, bool* enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool ret = call_helper_.SyncCall<bool>(g_sdk_api.IsCanvasItemClip, visual_id.c_str());
    if (enable)
        *enable = ret;
    return true;
}

bool MediaSDKControllerImplV2::SetVirtualCameraFlipV(const bool flip)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVirtualCameraFlipV, flip);
    return success;
}

bool MediaSDKControllerImplV2::SetVirtualCameraFlipH(const bool flip)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVirtualCameraFlipH, flip);
	return success;
}

bool MediaSDKControllerImplV2::SetVirtualCameraRotate(const float angle)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVirtualCameraRotate, angle);
	return success;
}

bool MediaSDKControllerImplV2::SetVisualDestroyedWhenAllReferenceRemoved(const std::string& visual_id, bool destroy)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualDestroyedWhenAllReferenceRemoved, visual_id.c_str(), destroy);
    return success;
}

bool MediaSDKControllerImplV2::GetMediaFileInfo(const std::string& file_path, MATERIAL_DESC* desc)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MediaSDKString result = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetVideoFileFrameInfo, file_path.c_str());
    bool success = false;
    try
    {
        do
        {
            nlohmann::json output_json = nlohmann::json::parse(result.ToString());
            if (output_json.empty())
                break;
            if (desc)
            {
                desc->angle = output_json["angle"];
                desc->size.Width = output_json["frame_width"];
                desc->size.Height = output_json["frame_height"];
                desc->duration = output_json["duration"];
                desc->path = file_path;
            }
            success = true;

        } while (0);
    }
    catch (...)
    {
        LOG(ERROR) << "parse GetMediaFileInfo output error! result = " << result.ToString();
    }
    return success;
}

bool MediaSDKControllerImplV2::SelectLayer(const std::string& canvas_id, const std::string& layer_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::SelectLayer)
    std::vector<uint32_t> video_models{};
    Mode* pMode = ModeSceneMgr::GetInstance()->GetCurrentMode();
    if (pMode)
    {
        MODE_INFO_EX modeInfoEx{};
        pMode->GetModeInfo(&modeInfoEx);
        if (modeInfoEx.id == LIVE_MODE_LANDSCAPE)
        {
            video_models.push_back(0);
        }
        else if (modeInfoEx.id == LIVE_MODE_PORTRAIT)
        {
            video_models.push_back(1);
        }
        else if (modeInfoEx.id == LIVE_MODE_DBCANVAS)
        {
            video_models.push_back(2);
            video_models.push_back(3);
        }
    }

    bool        success = false;
    std::string cur_canvas_item_id = "";
    for (const auto& video_model : video_models)
    {
        std::string cur_canvas_id = "";
        GetCurrentCanvas(video_model, &cur_canvas_id);
        if (cur_canvas_id == canvas_id)
        {
            success = call_helper_.SyncCall<bool>(g_sdk_api.SetCurrentCanvasItem, cur_canvas_id.c_str(), layer_id.c_str());
            click_track_id_ = video_model;
        }
        else
        {
            success = call_helper_.SyncCall<bool>(g_sdk_api.SetCurrentCanvasItem, cur_canvas_id.c_str(), "");
        }
        
        if (!success)
        {
            LOG(ERROR) << "[MediaSDKControllerImplV2::SelectLayer] SetCurrentCanvasItem error!";
            break;
        }
    }
    return success;
}

bool MediaSDKControllerImplV2::LayerSetLock(const std::string& layer_id, bool lock)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (lock)
    {
        bool success = call_helper_.SyncCall<bool>(g_sdk_api.LockCanvasItem, layer_id.c_str());
        return success;
    }
    else
    {
        bool success = call_helper_.SyncCall<bool>(g_sdk_api.UnLockCanvasItem, layer_id.c_str());
        return success;
    }
}

bool MediaSDKControllerImplV2::LayerClipMask(const std::string& layer_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.BeginCanvasItemClip, layer_id.c_str());
    return success;
}

bool MediaSDKControllerImplV2::LayerSetOrder(const uint32_t video_model, const std::vector<std::string>& layer_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::vector<mediasdk::MediaSDKString> l1;
    for (const auto& layer_id : layer_list)
    {
        l1.push_back(mediasdk::MediaSDKString(layer_id));
    }
    mediasdk::MediaSDKStringArray list(l1);
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemByOrderIDSOnVideoModel, video_model, list);

    return success;
}

bool MediaSDKControllerImplV2::LayerMoveTop(const std::string& layer_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.MoveCanvasItemZOrder, layer_id.c_str(), mediasdk::MovePostion::kMoveTop);
    return success;
}

bool MediaSDKControllerImplV2::LayerSetMoveRange(const std::string& layer_id, const float x0, const float y0, const float x1, const float y1)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSClipF clip;
    clip.x = x0;
    clip.y = y0;
    clip.z = x1;
    clip.w = y1;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemMoveRange, layer_id.c_str(), clip);
    return success;
}

bool MediaSDKControllerImplV2::LayerSetMinScale(const std::string& layer_id, float min_scale_x, float min_scale_y)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSScaleF s;
    s.x = min_scale_x;
    s.y = min_scale_y;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemMinScale, layer_id.c_str(), s);
    return success;
}

bool MediaSDKControllerImplV2::LayerGetMinScale(const std::string& visual_id, float* min_scale_x, float* min_scale_y)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolMSScaleF>(g_sdk_api.GetCanvasItemMinScale, visual_id.c_str());
    if (ret.success)
    {
        if (min_scale_x)
            *min_scale_x = ret.value.x;
        if (min_scale_y)
            *min_scale_y = ret.value.y;
    }
    return ret.success;
}

bool MediaSDKControllerImplV2::LayerSetMaxScale(const std::string& visual_id, float max_scale_x, float max_scale_y)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::LayerSetMaxScale)
    mediasdk::MSScaleF s;
    s.x = max_scale_x;
    s.y = max_scale_y;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemMaxScale, visual_id.c_str(), s);
    return success;
}

bool MediaSDKControllerImplV2::LayerGetMaxScale(const std::string& visual_id, float* max_scale_x, float* max_scale_y)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::LayerGetMaxScale)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolMSScaleF>(g_sdk_api.GetCanvasItemMaxScale, visual_id.c_str());

    if (ret.success)
    {
        if (max_scale_x)
            *max_scale_x = ret.value.x;
        if (max_scale_y)
            *max_scale_y = ret.value.y;
    }

    return ret.success;
}

bool MediaSDKControllerImplV2::LayerGetTransform(const std::string& layer_id, TRANSFORM* transform_info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolMSTransform>(g_sdk_api.GetCanvasItemTransform, layer_id.c_str());
    if (ret.success)
    {
        if (transform_info)
        {
            transform_info->translate.X = ret.value.translate.x;
            transform_info->translate.Y = ret.value.translate.y;
            transform_info->hFlip = ret.value.flip_h;
            transform_info->vFlip = ret.value.flip_v;
            transform_info->angle = ret.value.angle;
            transform_info->scale.X = ret.value.scale.x;
            transform_info->scale.Y = ret.value.scale.y;
            transform_info->clipRange.x = ret.value.clip.x;
            transform_info->clipRange.y = ret.value.clip.y;
            transform_info->clipRange.z = ret.value.clip.z;
            transform_info->clipRange.w = ret.value.clip.w;
        }
    }
    return ret.success;
}

bool MediaSDKControllerImplV2::LayerSetTransform(const std::string& layer_id, const TRANSFORM& info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSTransform transform;
    transform.translate.x = info.translate.X;
    transform.translate.y = info.translate.Y;
    transform.flip_h = info.hFlip;
    transform.flip_v = info.vFlip;
    transform.angle = info.angle;
    transform.scale.x = info.scale.X;
    transform.scale.y = info.scale.Y;
    transform.clip.x = info.clipRange.x;
    transform.clip.y = info.clipRange.y;
    transform.clip.z = info.clipRange.z;
    transform.clip.w = info.clipRange.w;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemTransform, layer_id.c_str(), transform);
    return success;
}

bool MediaSDKControllerImplV2::LayerSetPosition(const std::string& layer_id, const float x, const float y)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSTranslateF translate;
    translate.x = x;
    translate.y = y;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemTranslate, layer_id.c_str(), translate);
    return success;
}

bool MediaSDKControllerImplV2::VisualGetPosition(const std::string& visual_id, float* x, float* y)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolMSTranslateF>(g_sdk_api.GetCanvasItemTranslate, visual_id.c_str());
    if (ret.success)
    {
        if (x)
            *x = ret.value.x;
        if (y)
            *y = ret.value.y;
    }
    return ret.success;
}

bool MediaSDKControllerImplV2::LayerSetScale(const std::string& layer_id, float scale_x, float scale_y)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSScaleF scale;
    scale.x = scale_x;
    scale.y = scale_y;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemScale, layer_id.c_str(), scale);
    return success;
}

bool MediaSDKControllerImplV2::LayerGetScale(const std::string& layer_id, float* scale_x, float* scale_y)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolMSScaleF>(g_sdk_api.GetCanvasItemScale, layer_id.c_str());
    if (ret.success)
    {
        if (scale_x)
            *scale_x = ret.value.x;
        if (scale_y)
            *scale_y = ret.value.y;
    }
    return ret.success;
}

bool MediaSDKControllerImplV2::VisualFilterDestroy(const std::string& layer_id, const std::string& filter_id, const std::string& filter_type /*= ""*/)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::string filterId = layer_id + "_" + filter_id;
    bool        success = false;
    if (filter_type == "color_adjust")
    {
        std::string color_adjust_filter_id = filterId + "_coloradjust";
        LOG(INFO) << "[MediaSDKControllerImplV2::VisualFilterDestroy] visual_id: " << layer_id << ", filter_id: " << color_adjust_filter_id << ", filter_type: " << filter_type;
        success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyVisualFilter, color_adjust_filter_id.c_str(), layer_id.c_str());
    }
    else if (filter_type == "chroma_key")
    {
        std::string chroma_key_filter_id = filterId + "_chromakey";
        LOG(INFO) << "[MediaSDKControllerImplV2::VisualFilterDestroy] visual_id: " << layer_id << ", filter_id: " << chroma_key_filter_id << ", filter_type: " << filter_type;
        success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyVisualFilter, chroma_key_filter_id.c_str(), layer_id.c_str());
        if (!success)
            LOG(ERROR) << "[MediaSDKControllerImplV2::VisualFilterDestroy] chroma_key_filter destory failed";
    }
    else if (filter_type == "color_lut")
    {
        std::string colorLutFilterID = filterId + "_color_lut";
        LOG(INFO) << "[MediaSDKControllerImplV2::VisualFilterDestroy] visual_id: " << layer_id << ", filter_id: " << colorLutFilterID << ", filter_type: " << filter_type;
        success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyVisualFilter, colorLutFilterID.c_str(), layer_id.c_str());
    }
    else if (filter_type == "sharpness")
    {
        std::string sharpnessFilterID = filterId + "_sharpness";
        LOG(INFO) << "[MediaSDKControllerImplV2::VisualFilterDestroy] visual_id: " << layer_id << ", filter_id: " << sharpnessFilterID << ", filter_type: " << filter_type;
        success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyVisualFilter, sharpnessFilterID.c_str(), layer_id.c_str());
    }
    else
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyVisualFilter, filterId.c_str(), layer_id.c_str());
    }

    if (!success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::VisualFilterDestroy] DestroyVisualFilter failed";
    return success;
}

bool MediaSDKControllerImplV2::VisualFilterSetActive(const std::string& layer_id, const std::string& filter_id, bool active, const std::string& filter_type /*= ""*/)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::string filterId = layer_id + "_" + filter_id;
    bool        success = false;
    if (filter_type == "color_adjust")
    {
        std::string color_adjust_filter_id = filterId + "_coloradjust";
        LOG(INFO) << "[MediaSDKControllerImplV2::VisualFilterSetActive] visual_id: " << layer_id << ", filter_id: " << color_adjust_filter_id << ", filter_type: " << filter_type;
        success = call_helper_.SyncCall<bool>(g_sdk_api.VisualFilterSetActive, color_adjust_filter_id.c_str(), layer_id.c_str(), active);
    }
    else if (filter_type == "chroma_key")
    {
        std::string chroma_key_filter_id = filterId + "_chromakey";
        LOG(INFO) << "[MediaSDKControllerImplV2::VisualFilterSetActive] visual_id: " << layer_id << ", filter_id: " << chroma_key_filter_id << ", filter_type: " << filter_type;
        success = call_helper_.SyncCall<bool>(g_sdk_api.VisualFilterSetActive, chroma_key_filter_id.c_str(), layer_id.c_str(), active);
        if (!success)
            LOG(ERROR) << "[MediaSDKControllerImplV2::VisualFilterSetActive] VisualFilterSetActive chroma_key_filter setActive failed";
    }
    else if (filter_type == "color_lut")
    {
        std::string colorLutFilterID = filterId + "_color_lut";
        LOG(INFO) << "[MediaSDKControllerImplV2::VisualFilterSetActive] visual_id: " << layer_id << ", filter_id: " << colorLutFilterID << ", filter_type: " << filter_type;
        success = call_helper_.SyncCall<bool>(g_sdk_api.VisualFilterSetActive, colorLutFilterID.c_str(), layer_id.c_str(), active);
    }
    else if (filter_type == "sharpness")
    {
        std::string sharpnessFilterID = filterId + "_sharpness";
        LOG(INFO) << "[MediaSDKControllerImplV2::VisualFilterSetActive] visual_id: " << layer_id << ", filter_id: " << sharpnessFilterID << ", filter_type: " << filter_type;
        success = call_helper_.SyncCall<bool>(g_sdk_api.VisualFilterSetActive, sharpnessFilterID.c_str(), layer_id.c_str(), active);
    }
    else
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.VisualFilterSetActive, filterId.c_str(), layer_id.c_str(), active);
    }

    if (!success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::VisualFilterSetActive] VisualFilterSetActive failed";
    return success;
}

bool MediaSDKControllerImplV2::CreateCornerVisualFilter(const std::string& visual_id, const std::string& filter_id, const CornerFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    {
        nlohmann::json json_radius;
        json_radius["x"] = param.cornerInfo.x;
        json_radius["y"] = param.cornerInfo.y;
        json_radius["w"] = param.cornerInfo.w;
        json_radius["z"] = param.cornerInfo.z;
        json_root["radius"] = json_radius;
    }
    json_root["fixed_radius"] = param.fixedRadius;
    json_root["refer_width"] = param.referenceBorderWidth;
    std::string filterId = visual_id + "_" + filter_id;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, filterId.c_str(), "CornerVisualFilter", visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateEdgeVisualFilter(const std::string& visual_id, const std::string& filter_id, const EdgeFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    {
        nlohmann::json json_color;
        json_color["x"] = param.edgeColor.x;
        json_color["y"] = param.edgeColor.y;
        json_color["z"] = param.edgeColor.z;
        json_color["w"] = param.edgeColor.w;
        json_root["edge_color"] = json_color;
    }
    {
        nlohmann::json json_width;
        json_width["x"] = param.edgeWidth;
        json_width["y"] = param.edgeWidth;
        json_root["width"] = json_width;
    }
    std::string filterId = visual_id + "_" + filter_id;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, filterId.c_str(), "BorderVisualFilter", visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateColorAdjustVisualFilter(const std::string& visual_id, const std::string& filter_id, const ColorAdjustFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["brightness"] = param.brightness;
    json_root["contrast"] = param.contrast;
    json_root["gamma"] = param.gamma;
    json_root["hue_shift"] = param.hueShift;
    json_root["opacity"] = param.opacity;
    json_root["saturation"] = param.saturation;
    json_root["add_color"] = param.addColor;
    json_root["mul_color"] = param.mulColor;
    std::string filterId = visual_id + "_" + filter_id + "_coloradjust";
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, filterId.c_str(), "ColorAdjustmentVisualFilter", visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateChromaKeyVisualFilter(const std::string& visual_id, const std::string& filter_id, const ChromaKeyFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json chroma_key_json;
    chroma_key_json["chroma_key"] = param.chroma;
    chroma_key_json["similarity"] = param.similarity;
    chroma_key_json["smoothness"] = param.smoothness;
    chroma_key_json["spill"] = param.spill;
    chroma_key_json["brightness"] = param.brightness;
    chroma_key_json["contrast"] = param.contrast;
    chroma_key_json["gamma"] = param.gamma;
    chroma_key_json["opacity"] = param.opacity;
    std::string chromaKeyFilterID = visual_id + "_" + filter_id + "_chromakey";
    LOG(INFO) << "[MediaSDKControllerImplV2::CreateChromaKeyVisualFilter] visual_id: " << visual_id << ", filter_id: " << chromaKeyFilterID << ", chroma_key_json: " << chroma_key_json.dump().c_str();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, chromaKeyFilterID.c_str(), "ChromaKeyVisualFilter", visual_id.c_str(), chroma_key_json.dump().c_str());
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateChromaKeyVisualFilter] CreateVisualFilter chromaKey filter failed";
        return false;
    }
    return success;
}

bool MediaSDKControllerImplV2::CreateShapeVisualFilter(const std::string& visual_id, const std::string& filter_id, const ShapeFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json shape_json;
    shape_json["strech_starting_point"] = param.startPoint;
    std::string shapeFilterID = visual_id + "_" + filter_id;
    LOG(INFO) << "[MediaSDKControllerImplV2::CreateShapeVisualFilter] visual_id: " << visual_id << ", filter_id: " << shapeFilterID << ", shape_json: " << shape_json.dump().c_str();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, shapeFilterID.c_str(), "ShapeVisualFilter", visual_id.c_str(), shape_json.dump().c_str());
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateScaleVisualFilter] CreateVisualFilter shape filter failed";
        return false;
    }
    return success;
}

bool MediaSDKControllerImplV2::CreateScaleVisualFilter(const std::string& visual_id, const std::string& filter_id, const ScaleFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json scale_json;
    scale_json["strech_starting_point"] = param.startPoint;
    scale_json["strech_ratio"] = param.ratio;

    std::string scaleFilterID = visual_id + "_" + filter_id;
    LOG(INFO) << "[MediaSDKControllerImplV2::CreateScaleVisualFilter] visual_id: " << visual_id << ", filter_id: " << scaleFilterID << ", scale_json: " << scale_json.dump().c_str();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, scaleFilterID.c_str(), "ScaleVisualFilter", visual_id.c_str(), scale_json.dump().c_str());
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateScaleVisualFilter] CreateVisualFilter scale filter failed";
        return false;
    }
    return success;
}

bool MediaSDKControllerImplV2::CreateColorLutVisualFilter(const std::string& visual_id, const std::string& filter_id, const ColorLutFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["file_path"] = param.file_path.has_value() ? param.file_path.value() : "";
    json_root["amount"] = param.amount.has_value() ? param.amount.value() : 1.0f;
    json_root["pass_through_alpha"] = param.pass_through_alpha.has_value() ? param.pass_through_alpha.value() : false;
    std::string colorLutFilterID = visual_id + "_" + filter_id + "_color_lut";
    LOG(INFO) << "[MediaSDKControllerImplV2::CreateColorLutVisualFilter] visual_id: " << visual_id << ", filter_id: " << colorLutFilterID << ", color_lut_json: " << json_root.dump().c_str();
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, colorLutFilterID.c_str(), "ColorLutVisualFilter", visual_id.c_str(), json_root.dump().c_str());
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateColorLutVisualFilter] CreateVisualFilter colorLut filter failed";
        return false;
    }
    return success;
}

bool MediaSDKControllerImplV2::CreateSharpnessVisualFilter(const std::string& visual_id, const std::string& filter_id, const SharpnessFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["sharpness"] = param.sharpness;
    std::string colorLutFilterID = visual_id + "_" + filter_id + "_sharpness";
    LOG(INFO) << "[MediaSDKControllerImplV2::CreateSharpnessVisualFilter] visual_id: " << visual_id << ", filter_id: " << colorLutFilterID << ", sharpness_json: " << json_root.dump().c_str();
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, colorLutFilterID.c_str(), "SharpnessVisualFilter", visual_id.c_str(), json_root.dump().c_str());
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateSharpnessVisualFilter] CreateVisualFilter sharpness filter failed";
        return false;
    }
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterSaturation(const std::string& layer_id, const std::string& filter_id, float saturation)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["saturation"] = saturation;
    std::string filterId = layer_id + "_" + filter_id + "_coloradjust";
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "saturation", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterHueShift(const std::string& layer_id, const std::string& filter_id, float hue_shift)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["hue_shift"] = hue_shift;
    std::string filterId = layer_id + "_" + filter_id + "_coloradjust";
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "hue_shift", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterAddColor(const std::string& layer_id, const std::string& filter_id, uint32_t add_color)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["add_color"] = add_color;
    std::string filterId = layer_id + "_" + filter_id + "_coloradjust";
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "add_color", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterMulColor(const std::string& layer_id, const std::string& filter_id, uint32_t mul_color)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["mul_color"] = mul_color;
    std::string filterId = layer_id + "_" + filter_id + "_coloradjust";
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "mul_color", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterBrightness(const std::string& layer_id, const std::string& filter_id, const std::string& filter_type, float brightness)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["brightness"] = brightness;
    std::string filterId = layer_id + "_" + filter_id + "_" + filter_type;
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "brightness", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterGamma(const std::string& layer_id, const std::string& filter_id, const std::string& filter_type, float gamma)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["gamma"] = gamma;
    std::string filterId = layer_id + "_" + filter_id + "_" + filter_type;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "gamma", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterContrast(const std::string& layer_id, const std::string& filter_id, const std::string& filter_type, float contrast)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["contrast"] = contrast;
    std::string filterId = layer_id + "_" + filter_id + "_" + filter_type;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "contrast", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterOpacity(const std::string& layer_id, const std::string& filter_id, const std::string& filter_type, float opacity)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["opacity"] = opacity;
    std::string filterId = layer_id + "_" + filter_id + "_" + filter_type;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "opacity", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterChroma(const std::string& layer_id, const std::string& filter_id, uint32_t chroma)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["chroma_key"] = chroma;
    std::string filterId = layer_id + "_" + filter_id + "_chromakey";
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "chroma_key", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterSimilarity(const std::string& layer_id, const std::string& filter_id, float similarity)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["similarity"] = similarity;
    std::string filterId = layer_id + "_" + filter_id + "_chromakey";
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "similarity", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterSmoothness(const std::string& layer_id, const std::string& filter_id, float smoothness)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["smoothness"] = smoothness;
    std::string filterId = layer_id + "_" + filter_id + "_chromakey";
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "smoothness", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetColorFilterSpill(const std::string& layer_id, const std::string& filter_id, float spill)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["spill"] = spill;
    std::string filterId = layer_id + "_" + filter_id + "_chromakey";
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "spill", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateOverlayVisualFilter(const std::string& visual_id, const std::string& filter_id, const OverlayFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["file_path"] = param.bkImage;
    json_root["scale"] = param.bkScale;
    nlohmann::json point_nine;
    point_nine["left_start"] = param.pointNine.left.X;
    point_nine["left_end"] = param.pointNine.left.Y;
    point_nine["top_start"] = param.pointNine.top.X;
    point_nine["top_end"] = param.pointNine.top.Y;
    json_root["point_nine"] = point_nine;

    std::string filterId = visual_id + "_" + filter_id;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, filterId.c_str(), "ProspectVisualFilter", visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetOverlayFilterProperty(const std::string& layer_id, const std::string& filter_id, const OverlayFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["file_path"] = param.bkImage;
    json_root["scale"] = param.bkScale;
    nlohmann::json point_nine;
    point_nine["left_start"] = param.pointNine.left.X;
    point_nine["left_end"] = param.pointNine.left.Y;
    point_nine["top_start"] = param.pointNine.top.X;
    point_nine["top_end"] = param.pointNine.top.Y;
    json_root["point_nine"] = point_nine;

    std::string filterId = layer_id + "_" + filter_id;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateHintVisualFilter(const std::string& visual_id, const std::string& filter_id, const HintFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["gap"] = param.gap;

    json_root["image_path"] = param.imagePath;
    json_root["image_width"] = param.imageWidth;
    json_root["image_height"] = param.imageHeight;
    json_root["animation_iteration_count"] = param.animationIterationCnt;

    json_root["text"] = param.text;
    json_root["text_width"] = param.text;
    json_root["font_size"] = param.fontSize;
    json_root["font_family"] = param.fontFamily;
    json_root["font_file_path"] = param.fontFilePath;
    json_root["font_color"] = param.fontColor;

    std::string filterId = visual_id + "_" + filter_id;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, filterId.c_str(), "DisplayVisualFilter", visual_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetHintFilterProperty(const std::string& layer_id, const std::string& filter_id, const HintFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["gap"] = param.gap;

    json_root["image_path"] = param.imagePath;
    json_root["image_width"] = param.imageWidth;
    json_root["image_height"] = param.imageHeight;
    json_root["animation_iteration_count"] = param.animationIterationCnt;

    json_root["text"] = param.text;
    json_root["text_width"] = param.text;
    json_root["font_size"] = param.fontSize;
    json_root["font_family"] = param.fontFamily;
    json_root["font_file_path"] = param.fontFilePath;
    json_root["font_color"] = param.fontColor;

    std::string filterId = layer_id + "_" + filter_id;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::ResetFilterOrder(const std::string& layer_id, const std::vector<std::string>& filter_ids)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::vector<mediasdk::MediaSDKString> filter_id_vec;
    for (auto it : filter_ids)
    {
        filter_id_vec.push_back(it.c_str());
    }
    mediasdk::MediaSDKStringArray filter_id_array(filter_id_vec);
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFiltersPriority, layer_id.c_str(), filter_id_array);
    if (!success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::ResetFilterOrder] failed";
    return success;
}

bool MediaSDKControllerImplV2::SetColorLutFilterProperty(const std::string& layer_id, const std::string& filter_id, const ColorLutFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    bool success = false;
    std::string colorLutFilterID = layer_id + "_" + filter_id + "_color_lut";
    if (param.file_path.has_value())
    {
        json_root["file_path"] = param.file_path.value();
        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, colorLutFilterID.c_str(), layer_id.c_str(), "file_path", json_root.dump().c_str());
    }
    if (!success)
        return false;
    if (param.amount.has_value())
    {
        json_root["amount"] = param.amount.value();
        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, colorLutFilterID.c_str(), layer_id.c_str(), "amount", json_root.dump().c_str());
    }
    if (!success)
        return false;

    if (param.pass_through_alpha.has_value())
    {
        json_root["pass_through_alpha"] = param.pass_through_alpha.value();
        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, colorLutFilterID.c_str(), layer_id.c_str(), "pass_through_alpha", json_root.dump().c_str());
    }
    return success;
}

bool MediaSDKControllerImplV2::SetSharpnessFilterProperty(const std::string& layer_id, const std::string& filter_id, const SharpnessFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    bool success = false;
    std::string filterID = layer_id + "_" + filter_id + "_sharpness";
    json_root["sharpness"] = param.sharpness;
    success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterID.c_str(), layer_id.c_str(), "sharpness", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetScaleFilterProperty(const std::string& layer_id, const std::string& filter_id, const ScaleFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    bool           success = false;
    std::string    filterID = layer_id + "_" + filter_id;
    json_root["strech_starting_point"] = param.startPoint;
    json_root["strech_ratio"] = param.ratio;
    success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterID.c_str(), layer_id.c_str(), "", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetShapeFilterProperty(const std::string& layer_id, const std::string& filter_id, const ShapeFilter& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    bool success = false;
    std::string filterID = layer_id + "_" + filter_id;
    json_root["strech_starting_point"] = param.startPoint;
    success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterID.c_str(), layer_id.c_str(), "strech_starting_point", json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::AudioFilterDestroy(const std::string& audio_id, const std::string& filter_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyAudioFilter, filter_id.c_str(), audio_id.c_str());
    if (success)
    {
        if (SamiNS_model_path_map_.count(filter_id) != 0)
        {
            SamiNS_model_path_map_.erase(filter_id);
        }
        if (Sami_filter_id_map_.count(filter_id) != 0)
        {
            Sami_filter_id_map_.erase(filter_id);
        }
    }
    return success;
}

bool MediaSDKControllerImplV2::AudioFilterSetEnable(const std::string& audio_id, const std::string& filter_id, bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioFilterEnable, filter_id.c_str(), audio_id.c_str(), enable);
    return success;
}

bool MediaSDKControllerImplV2::AudioFilterGetEnable(const std::string& audio_id, const std::string& filter_id, bool* enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.IsAudioFilterEnable, filter_id.c_str(), audio_id.c_str());
    if (!ret.success)
    {
        LOG(ERROR) << "AudioFilterGetEnable error!";
        return false;
    }
    if (enable)
        *enable = ret.value;
    return true;
}

bool MediaSDKControllerImplV2::CreateSamiCommonMetricsAudioFilter(const std::string& audio_id, const std::string& filter_id, const SAMI_COMMON_METRICS_FILTER& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_param;
    json_param["enable"] = true;
    json_param["model_file_path"] = param.configFile;
    json_param["config_metrics_json"] = param.configJson;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateAudioFilter, filter_id.c_str(), "SAMICommonMetricsAudioFilter", audio_id.c_str(), json_param.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SamiCommonMetricsAudioFilterGetCommonMetrics(const std::string& audio_id, const std::string& filter_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::ResultBoolString ret = call_helper_.SyncCall<mediasdk::ResultBoolString>(g_sdk_api.GetAudioFilterProperty, filter_id.c_str(), audio_id.c_str(), "metrics_result");
    return ret.success;
}

bool MediaSDKControllerImplV2::SamiCommonMetricsAudioFilterReset(const std::string& audio_id, const std::string& filter_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.AudioFilterAction, filter_id.c_str(), audio_id.c_str(), "reset_metrics_statistics", "ignore");
    return success;
}

bool MediaSDKControllerImplV2::CreateSamiNoiseSuppressAudioFilter(const std::string& audio_id, const std::string& filter_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    Sami_create_flag_ = true;
    return true; // wait for set model path....
}

bool MediaSDKControllerImplV2::SamiNoiseSuppressAudioFilterSetModel(const std::string& audio_id, const std::string& filter_id, const std::string& config_file)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::SamiNoiseSuppressAudioFilterSetModel)
    SamiNS_model_path_map_[filter_id] = config_file;
    return CreateSamiNoiseSuppressAudioFilterActually(audio_id, filter_id);
}

bool MediaSDKControllerImplV2::SamiNoiseSuppressAudioFilterSetSpeechRatio(const std::string& audio_id, const std::string& filter_id, const float speech_ratio)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::SamiNoiseSuppressAudioFilterSetSpeechRatio)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioFilterProperty, filter_id.c_str(), audio_id.c_str(), "noise_suppress_level", std::to_string(speech_ratio * 100).c_str());
    return success;
}

bool MediaSDKControllerImplV2::CreateSpeexNoiseSuppressAudioFilter(const std::string& audio_id, const std::string& filter_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_param;
    json_param["enable"] = true;
    json_param["noise_suppress_level"] = -30;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateAudioFilter, filter_id.c_str(), "SpeexNoiseSuppressAudioFilter", audio_id.c_str(), json_param.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SpeexNoiseSuppressAudioFilterSetSuppressLevel(const std::string& audio_id, const std::string& filter_id, int32_t suppress_level)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::SpeexNoiseSuppressAudioFilterSetSuppressLevel)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioFilterProperty, filter_id.c_str(), audio_id.c_str(), "noise_suppress_level", std::to_string(suppress_level).c_str());
    return success;
}

bool MediaSDKControllerImplV2::SpeexNoiseSuppressAudioFilterGetSuppressLevel(const std::string& audio_id, const std::string& filter_id, int32_t* suppress_level)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::SpeexNoiseSuppressAudioFilterGetSuppressLevel)
    mediasdk::ResultBoolString ret = call_helper_.SyncCall<mediasdk::ResultBoolString>(g_sdk_api.GetAudioFilterProperty, filter_id.c_str(), audio_id.c_str(), "noise_suppress_level");
    if (!ret.success)
    {
        LOG(ERROR) << "SpeexNoiseSuppressAudioFilterGetSuppressLevel error!";
        return false;
    }
    if (suppress_level)
        *suppress_level = std::stoi(ret.value.ToString());
    return true;
}

bool MediaSDKControllerImplV2::CreateSamiMdspEffectAudioFilter(const std::string& audio_id, const std::string& filter_id, bool enable, const SAMI_MDSP_EFFECT_FILTER& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_param;
    json_param["enable"] = enable;
    json_param["model_file_path"] = param.modelFile;
    json_param["res_file_path"] = param.resFilePath;
    json_param["param_json"] = param.mdspParam;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateAudioFilter, filter_id.c_str(), "SAMIMdspEffectAudioFilter", audio_id.c_str(), json_param.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::SamiMdspEffectAudioFilterSetParam(const std::string& audio_id, const std::string& filter_id, std::string param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::SamiMdspEffectAudioFilterSetParam)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioFilterProperty, filter_id.c_str(), audio_id.c_str(), "SetParam", param.c_str());
    return success;
}

bool MediaSDKControllerImplV2::EffectPlatformInit(const INIT_EFFECT_PLATFORM& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    json_root["appVersion"] = param.appVersion;
    json_root["deviceType"] = param.deviceType;
    json_root["appId"] = param.appID;
    json_root["accessKey"] = param.accessKey;
    json_root["channel"] = param.channel;
    json_root["effectCacheDir"] = param.effectCacheDir;
    json_root["modelCacheDir"] = param.modelCacheDir;
    json_root["builtInModelDir"] = param.builtInModelDir;
    json_root["lokiHost"] = param.lokiHost;
    json_root["veCloudHost"] = param.veCloudHost;
    json_root["modelStatus"] = param.modelStatus;
    json_root["region"] = param.region;
    json_root["deviceId"] = param.deviceID;
    json_root["userId"] = param.userID;
    json_root["ttlsHardwareLevel"] = param.ttlsHardwareLevel;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.EffectPlatformInitialize, json_root.dump().c_str());
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::EffectPlatformInit] EffectPlatformInitialize error!!";
        return false;
    }
    success = call_helper_.SyncCall<bool>(g_sdk_api.EffectPlatformRegistObserver, sdk_callback_mgr_.get());
    return success;
}

bool MediaSDKControllerImplV2::EffectPlatformUninit()
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.EffectPlatformUninitialize);
    return success;
}

bool MediaSDKControllerImplV2::EffectPlatformLoadModels(const std::string& request_id, const std::string& model_name, const std::vector<std::string>& requirements)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::vector<mediasdk::MediaSDKString> stringVec;
    for (auto it : requirements)
    {
        stringVec.push_back(it.c_str());
    }
    mediasdk::MediaSDKStringArray stringArray(stringVec);
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.EffectPlatformLoadModels, request_id.c_str(), model_name.c_str(), &stringArray);
    return success;
}

bool MediaSDKControllerImplV2::EffectPlatformUpdateConfig(const std::string& user_id, const std::string& ttls_hardware_level)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.EffectPlatformUpdateConfig, user_id.c_str(), ttls_hardware_level.c_str());
    return success;
}

bool MediaSDKControllerImplV2::EffectEnable(const std::string& layer_id, bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::EffectEnable)
    auto filterId = layer_id + "_filter";
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.VisualFilterSetActive, filterId.c_str(), layer_id.c_str(), enable);
    return success;
}

bool MediaSDKControllerImplV2::EffectSetBkImage(const std::string& layer_id, const std::string& key, const std::string& filepath)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json j = {
        {"path", filepath},
        {"key", key},
    };
    std::string json_param = j.dump();
    auto        filterId = layer_id + "_filter";
    bool        ret = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kComposerSetBackgroundKey, json_param.c_str());
    return ret;
}

bool MediaSDKControllerImplV2::EffectComposerAdd(const std::string& layer_id, const std::vector<std::string>& composer_list, const std::vector<std::string>& tag_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto           filterId = layer_id + "_filter";
    nlohmann::json j = {
        {"paths", composer_list},
        {"tags", tag_list},
    };
    std::string    json_param = j.dump();
    bool           success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kComposerAdderKey, json_param.c_str());
    return success;
}

bool MediaSDKControllerImplV2::EffectComposerSet(const std::string& layer_id, const std::vector<std::string>& composer_list, const std::vector<std::string>& tag_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto           filterId = layer_id + "_filter";
    nlohmann::json j = {
        {"paths", composer_list},
        {"tags", tag_list},
    };
    std::string    json_param = j.dump();
    bool           success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kComposerSetterKey, json_param.c_str());
    return success;
}

bool MediaSDKControllerImplV2::EffectComposerRemove(const std::string& layer_id, const std::vector<std::string>& composer_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto           filterId = layer_id + "_filter";
    nlohmann::json j = composer_list;
    std::string    json_param = j.dump();
    bool           success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kComposerRemoverKey, json_param.c_str());
    return success;
}

bool MediaSDKControllerImplV2::EffectComposerUpdate(const std::string& layer_id, const std::string& path, const std::string& key, float value, const std::string& tag_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
        auto           filterId = layer_id + "_filter";
    nlohmann::json j = {
        {"path", path},
        {"key", key},
        {"value", value},
            {"tags", tag_list},
    };
    std::string json_param = j.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kComposerUpdaterKey, json_param.c_str());
    return success;
}

bool MediaSDKControllerImplV2::EffectComposerReplace(const std::string& layer_id, const std::vector<std::string>& old_composer_list, const std::vector<std::string>& new_composer_list, const std::vector<std::string>& tag_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto filterId = layer_id + "_filter";
    nlohmann::json j = {
        {"oldPaths", old_composer_list},
        {"newPaths", new_composer_list},
        {"tags", tag_list},
    };
    std::string json_param = j.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kComposerReplaceKey, json_param.c_str());
    return success;
}

bool MediaSDKControllerImplV2::EffectComposerSetText(const std::string& layer_id, const std::string& key, const std::string& value)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto           filterId = layer_id + "_filter";
    nlohmann::json j = {
        {"key", key},
        {"value", value},
    };
    std::string json_param = j.dump();
    bool        success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kComposerSetTextKey, json_param.c_str());

    return success;
}

bool MediaSDKControllerImplV2::EffectSetMsg(const std::string& layer_id, const EFFECT_MSG& effect_msg)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	nlohmann::json json_root;
	json_root["msgID"] = effect_msg.msgID;
	json_root["arg1"] = effect_msg.arg1;
	json_root["arg2"] = effect_msg.arg2;
	json_root["arg3"] = effect_msg.arg3;
	std::string json_param = json_root.dump();

    auto filterId = layer_id + "_filter";
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVisualFilterProperty, filterId.c_str(), layer_id.c_str(), "send_msg", json_root.dump().c_str());
	return success;
}

bool MediaSDKControllerImplV2::EffectComposerGetExclusion(const std::string& layer_id, const std::string& node_path, const std::string& node_tag, int32_t* res)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto           filterId = layer_id + "_filter";
    nlohmann::json j = {
        {"nodePath", node_path},
        {"nodeTag", node_tag},
    };
    std::string              json_param = j.dump();
    mediasdk::MediaSDKString ret = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.VisualFilterAction, filterId.c_str(), layer_id.c_str(), mediasdk::effect_visual_filter::kComposerGetExclusionKey, json_param.c_str());
    bool                     success = false;
    try
    {
        nlohmann::json json_root;
        json_root = nlohmann::json::parse(ret.ToString());
        if (!json_root.empty())
        {
            if (res)
            {
                *res = json_root["isExclusion"];
            }
            success = true;
        }
    }
    catch (...)
    {
        LOG(ERROR) << "parse EffectComposerGetExclusion json error! origin ret is:" << ret.ToString();
    }
    return success;
}

bool MediaSDKControllerImplV2::EffectSetPicQualityBrightness(const std::string& visual_id, const BRIGHT_CONFIG& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0);
    bool success = false;
    std::string value = "";
    if (!param.keyVal.key.empty() && !param.keyVal.val.empty())
    {
        value = param.keyVal.key + ";" + param.keyVal.val;
    }
    std::string property = StringPrintf("%s;%s", param.assetPath.c_str(), value.c_str());

    if (param.enable)
    {
        success = EffectComposerAdd(visual_id, {property}, std::vector<std::string>{});
    }
    else
    {
        success = EffectComposerRemove(visual_id, {property});
    }
    return success;
}

bool MediaSDKControllerImplV2::CreateWASAPIAudioSource(const AUDIO_INFO& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::string audio_id = "";
    Util::NumToString(param.id, &audio_id);

    // TODO, for debug
    //{
    //    auto audio_frame_processor = hook_api_layer_->GetAudioFrameProcessor();

    //    // test AudioInput Listener
    //    audio_frame_processor->AudioInputStartListen(audio_id, v3::AudioFrameProcessor::AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw);

    //    // test CustomAudioInput
    //    static int add_count = 0;
    //    ++add_count;

    //    *((uint32_t*)&param.audioTrack) = 1 << 3;

    //    int main_track_id = 1;

    //    std::string custom_audio_input_id = StringPrintf("test_custom_audio_input_%d", add_count);

    //    g_sdk_api.SetAudioTrackDelayMs(main_track_id, 2200);
    //    audio_frame_processor->CreateEraseAudioInput(main_track_id, custom_audio_input_id, 2000);
    //    audio_frame_processor->EraseAudioInputSetReplaceAudio(custom_audio_input_id, "C:\\Users\\<USER>\\Downloads\\file_example_MP3_1MG.mp3");
    //    audio_frame_processor->EraseAudioInputSetSource(custom_audio_input_id, audio_id);

    //    // test delete CustomAudioInput
    //    std::thread test_delete_thread([audio_frame_processor, custom_audio_input_id]() {
    //        std::this_thread::sleep_for(std::chrono::seconds(20));
    //        audio_frame_processor->DeleteEraseAudioInput(custom_audio_input_id);
    //    });
    //    test_delete_thread.detach();
    //}
    
    mediasdk::CreateAudioParams pp;
    pp.track_id = 0;
    uint32_t track_ids = param.audioTrack;
    bool     res = GetFirstAudioTrackOfTrackIds(track_ids, pp.track_id);
    if (!res)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateWASAPIAudioSource] GetFirstAudioTrackOfTrackIds error! param.audioTrack = " << param.audioTrack;
    }
    if (pp.track_id > 0)
    {
        audio_id_map_[audio_id] = 1 << (pp.track_id - 1);
    }
    pp.plugin_name = wasapi_audio_source::GetPluginName();
    {
        nlohmann::json json_root;
        json_root["device_id"] = param.device.id.c_str();
        json_root["device_name"] = param.device.name.c_str();

        WAS_AUDIO was = std::get<WAS_AUDIO>(param.audio);
        json_root["audio_input_type"] = was.type == AUDIO_INPUT_MICROPHONE ? mediasdk::AudioInputType::kAudioInputMicrophone : mediasdk::AudioInputType::kAudioInputLoopback;
        pp.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
    }

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateAudioInput, audio_id.c_str(), pp);
    if (!success)
    {
        LOG(ERROR) << "MediaSDKControllerImplV2::CreateWASAPIAudioSource error! audio_id = " << audio_id;
        return false;
    }

    success = AudioSourceSetTracks(audio_id, param.audioTrack);
    if (!success)
    {
        LOG(ERROR) << "MediaSDKControllerImplV2::CreateWASAPIAudioSource error! audio_track_ids = " << param.audioTrack;
    }
    return success;
}

bool MediaSDKControllerImplV2::WASAPIAudioSourceEnumInputDevices(std::vector<DSHOW>* info_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumCaptureAudio, wasapi_audio_source::GetPluginName().data());
    bool success = false;
    try
    {
        nlohmann::json json_root;
        json_root = nlohmann::json::parse(ret.ToString());
        if (!json_root.empty())
        {
            if (info_list)
            {
                for (const auto& element : json_root)
                {
                    DSHOW info;
                    info.id = element["device_id"];
                    info.name = element["device_name"];
                    info_list->push_back(info);
                }
            }
            success = true;
        }
    }
    catch (...)
    {
        LOG(ERROR) << "parse WASAPIAudioSourceEnumInputDevices json error!";
    }
    return success;
}

bool MediaSDKControllerImplV2::WASAPIAudioSourceEnumOutputDevices(std::vector<DSHOW>* info_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumRenderAudio, wasapi_audio_source::GetPluginName().data());
    bool success = false;
    try
    {
        nlohmann::json json_root;
        json_root = nlohmann::json::parse(ret.ToString());
        if (!json_root.empty())
        {
            if (info_list)
            {
                for (const auto& element : json_root)
                {
                    DSHOW info;
                    info.id = element["device_id"];
                    info.name = element["device_name"];
                    info_list->push_back(info);
                }
            }

            success = true;
        }
    }
    catch (...)
    {
        LOG(ERROR) << "parse WASAPIAudioSourceEnumOutputDevices json error!";
    }
    return success;
}

bool MediaSDKControllerImplV2::WASAPIAudioSourceGetDefaultInputDevice(DSHOW* info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetDefaultCaptureAudio, wasapi_audio_source::GetPluginName().data());
    bool success = false;
    try
    {
        nlohmann::json json_root;
        json_root = nlohmann::json::parse(ret.ToString());
        if (!json_root.empty())
        {
            if (info)
            {
                info->id = json_root["device_id"];
                info->name = json_root["device_name"];
            }

            success = true;
        }
    }
    catch (...)
    {
        LOG(ERROR) << "parse WASAPIAudioSourceGetDefaultInputDevice json error!";
    }
    return success;
}

bool MediaSDKControllerImplV2::WASAPIAudioSourceGetDefaultOutputDevice(DSHOW* info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetDefaultRenderAudio, wasapi_audio_source::GetPluginName().data());
    bool success = false;
    try
    {
        nlohmann::json json_root;
        json_root = nlohmann::json::parse(ret.ToString());
        if (!json_root.empty())
        {
            if (info)
            {
                info->id = json_root["device_id"];
                info->name = json_root["device_name"];
            }
            success = true;
        }
    }
    catch (...)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::WASAPIAudioSourceGetDefaultOutputDevice] parse json error";
    }

    return success;
}

bool MediaSDKControllerImplV2::CreateAppAudioSource(const AUDIO_INFO& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::string audio_id = "";
    Util::NumToString(param.id, &audio_id);
    mediasdk::CreateAudioParams pp;
    pp.track_id = 0;
    uint32_t track_ids = param.audioTrack;
    bool     res = GetFirstAudioTrackOfTrackIds(track_ids, pp.track_id);
    if (!res)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateAppAudioSource] GetFirstAudioTrackOfTrackIds error! param.audioTrack = " << param.audioTrack;
    }
    if (pp.track_id > 0)
    {
        audio_id_map_[audio_id] = 1 << (pp.track_id - 1);
    }
    pp.plugin_name = app_audio_source::GetPluginName();

    {
        nlohmann::json json_root;
        uint32_t pid = 0;
        Util::StringToNum(param.device.id, &pid);
        json_root["pid"] = pid;
        json_root["exe_name"] = param.device.name.c_str();

        APP_AUDIO app = std::get<APP_AUDIO>(param.audio);
        json_root["exclude_mode"] = app.excludePID;
        json_root["audio_input_type"] = mediasdk::kAudioInputAPP;
        pp.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
    }

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateAudioInput, audio_id.c_str(), pp);
    if (!success)
    {
        LOG(ERROR) << "MediaSDKControllerImplV2::CreateAppAudioSource error! audio_id = " << audio_id;
        return false;
    }

    success = AudioSourceSetTracks(audio_id, param.audioTrack);
    if (!success)
    {
        LOG(ERROR) << "MediaSDKControllerImplV2::CreateAppAudioSource error! audio_track_ids = " << param.audioTrack;
    }

    return success;
}

bool MediaSDKControllerImplV2::AppAudioSourceEnum(std::vector<DSHOW>* output_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.EnumAudioInput, app_audio_source::GetPluginName().data());
    bool success = false;
    try
    {
        nlohmann::json json_root;
        json_root = nlohmann::json::parse(ret.ToString());
        if (!json_root.empty())
        {
            if (output_list)
            {
                nlohmann::json& json_app_session = json_root["app_session"];
                if (json_app_session.is_array())
                {
                    for (const auto& element : json_app_session)
                    {
                        DSHOW info{};
                        info.id = std::string(element["process_id"]);
                        info.name = element["app_name"];
                        info.exe = element["exe_full_path"];
                        output_list->push_back(info);
                    }
                }
            }
            success = true;
        }
    }
    catch (...)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::AppAudioSourceEnum] parse json error! json = " << ret.ToString();
    }

    return success;
}

bool MediaSDKControllerImplV2::AppAudioSourceIsSystemSupport(bool* support)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    static std::optional<bool> appAudioSupport = std::nullopt;
    if (appAudioSupport == std::nullopt)
    {
        auto                      pluginName = app_audio_source::GetPluginName();
        mediasdk::PluginInfoArray ret = call_helper_.SyncCall<mediasdk::PluginInfoArray>(g_sdk_api.EnumAudioInputSource);
        auto                      vec = ret.ToVector();
        for (auto it : vec)
        {
            if (it.name.ToString() == pluginName)
            {
                appAudioSupport = true;
                break;
            }
        }
        if (appAudioSupport == std::nullopt)
        {
            appAudioSupport = false;
        }
    }
    if (support)
    {
        *support = appAudioSupport.value();
    }

    return true;
}

bool MediaSDKControllerImplV2::CreatePCMAudioSource(const AUDIO_INFO& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::string audio_id = "";
    Util::NumToString(param.id, &audio_id);
    mediasdk::CreateAudioParams pp;
    pp.track_id = 0;
    uint32_t track_ids = param.audioTrack;
    bool     res = GetFirstAudioTrackOfTrackIds(track_ids, pp.track_id);
    if (!res)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreatePCMAudioSource] GetFirstAudioTrackOfTrackIds error! param.audioTrack = " << param.audioTrack;
    }
    if (pp.track_id > 0)
    {
        audio_id_map_[audio_id] = 1 << (pp.track_id - 1);
    }
    pp.plugin_name = mediasdk::MediaSDKString("PCMAudioSource");
    {
        nlohmann::json json_root;
        json_root["exe_name"] = "PCM";
        json_root["audio_input_type"] = mediasdk::kAudioInputPCM;
        json_root["sample_rate"] = param.audioCapture.samplePerSec;
        json_root["channel_num"] = param.audioCapture.channels;
        json_root["layout"] = param.audioCapture.channelLayout; // 2: CHANNEL_STEREO
        json_root["format"] = param.audioCapture.audioFormat; // 8: AUDIO_FORMAT_FLOAT_PLANAR
        pp.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
    }
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateAudioInput, audio_id.c_str(), pp);
    if (!success)
    {
        LOG(ERROR) << "MediaSDKControllerImplV2::CreatePCMAudioSource error! audio_id = " << audio_id;
        return false;
    }

    success = AudioSourceSetTracks(audio_id, param.audioTrack);
    if (!success)
    {
        LOG(ERROR) << "MediaSDKControllerImplV2::CreatePCMAudioSource error! audioTrack = " << param.audioTrack;
    }

    return success;
}

bool MediaSDKControllerImplV2::PCMAudioSourceUpdate(const std::string& audio_id, const AUDIO_INFO& data)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    PCM_AUDIO pcm = std::get<PCM_AUDIO>(data.audio);
    nlohmann::json pcm_audio_data_json;
    pcm_audio_data_json["left_channel_datas"] = base64::to_base64(pcm.bufLeft);
    pcm_audio_data_json["right_channel_datas"] = base64::to_base64(pcm.bufRight);

    nlohmann::json json_root;
    json_root["update_pcm_play_datas"] = pcm_audio_data_json;
    json_root["sample_rate"] = data.audioCapture.samplePerSec;
    json_root["channel_num"] = data.audioCapture.channels;
    json_root["layout"] = data.audioCapture.channelLayout;
    json_root["format"] = data.audioCapture.audioFormat;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.UpdatePCMAudioDatas, audio_id.c_str(), json_root.dump().c_str());
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceSetProperty(const std::string& audio_id, const AUDIO_SETTING& prop)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::AudioInputParams params{};
    params.balance = prop.balanceing;
    params.interval = prop.interval;
    params.mono = prop.downMixMono;
    params.mute = prop.mute;
    params.sync_offset = prop.syncOffset;
    params.volume = prop.volume;

    bool ok = false;
    params.monitor_type = MapAudioDeviceMonitorType2SDK(prop.monitorType, &ok);
    if (!ok)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::AudioSourceSetProperty] monitor_type transfer failed";
        return false;
    }

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioInputParams, audio_id.c_str(), params);
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceIsMute(const std::string& audio_id, bool* mute)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::ResultBoolBool ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.IsAudioMute, audio_id.c_str());
    if (ret.success && mute)
        *mute = ret.value;

    return ret.success;
}

bool MediaSDKControllerImplV2::AudioSourceSetMute(const std::string& audio_id, const bool mute)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioMute, audio_id.c_str(), mute);
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceDestroy(const std::string& audio_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (audio_track_not_remove_.count(audio_id) != 0)
    {
        audio_track_not_remove_.erase(audio_id);
        return true;
    }
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyAudioInput, audio_id.c_str());
    if (success && audio_id_map_.count(audio_id))
    {
        audio_id_map_.erase(audio_id);
    }
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceMixToMono(const std::string& audio_id, const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioDownmixMono, audio_id.c_str(), enable);
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceIsEnableMixToMono(const std::string& audio_id, bool* enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.IsAudioDownmixMono, audio_id.c_str());
    if (ret.success && enable)
        *enable = ret.value;
    return ret.success;
}

bool MediaSDKControllerImplV2::AudioSourceSetBalance(const std::string& audio_id, const float val)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioBalancing, audio_id.c_str(), val);
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceGetBalance(const std::string& audio_id, float* val)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolFloat>(g_sdk_api.GetAudioBalancing, audio_id.c_str());
    if (ret.success && val)
        *val = ret.value;
    return ret.success;
}

bool MediaSDKControllerImplV2::AudioSourceSetSyncOffset(const std::string& audio_id, const int32_t sync_offset)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioSyncOffset, audio_id.c_str(), sync_offset);
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceGetSyncOffset(const std::string& audio_id, uint32_t* sync_offset)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolInt32>(g_sdk_api.GetAudioSyncOffset, audio_id.c_str());
    if (ret.success && sync_offset)
        *sync_offset = ret.value;
    return ret.success;
}

bool MediaSDKControllerImplV2::AudioSourceSetTracks(const std::string& audio_id, uint32_t audio_track_ids)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    LOG(INFO) << "[MediaSDKControllerImplV2::AudioSourceSetTracks] audio_id = " << audio_id << " audio_track_ids = " << audio_track_ids;
    uint32_t         flag = 1;
    std::vector<int> addVec;
    std::vector<int> removeVec;
    if (audio_id_map_.count(audio_id)) // 如果已经添加过音轨，需要判断是添加还是删除
    {
        uint32_t old_track_ids = audio_id_map_[audio_id];
        for (int i = 0; i < AUDIO_TRACK_MAX; i++)
        {
            int old_track_flag = (old_track_ids & (flag << i)) > 0 ? 1 : 0;
            int new_track_flag = (audio_track_ids & (flag << i)) > 0 ? 1 : 0;
            if (new_track_flag - old_track_flag > 0)
            {
                addVec.push_back(i + 1);
            }
            else if (new_track_flag - old_track_flag < 0)
            {
                removeVec.push_back(i + 1);
            }
        }
    }
    else
    {
        for (int i = 0; i < AUDIO_TRACK_MAX; i++)
        {
            if ((audio_track_ids & (flag << i)) > 0)
            {
                addVec.push_back(i + 1);
            }
        }
    }
    
    if (audio_track_ids == 0)
    {
        addVec.push_back(audio_track_ids);
    }

    // 为了兼容sdk逻辑，需要先加后减
    if (!AudioSourceSetTracksActually(audio_id, addVec, removeVec))
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::AudioSourceSetTracks] AudioSourceSetTracksActually error!";
        return false;
    }
    audio_id_map_[audio_id] = audio_track_ids;
    return true;
}

bool MediaSDKControllerImplV2::AudioSourceSetVolumn(const std::string& audio_id, const float volume)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioVolume, audio_id.c_str(), volume);
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceGetVolumn(const std::string& audio_id, float* volume)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolFloat>(g_sdk_api.GetAudioVolume, audio_id.c_str());
    if (ret.success && volume)
        *volume = ret.value;
    return ret.success;
}

bool MediaSDKControllerImplV2::AudioSourceSetInterval(const std::string& audio_id, const int32_t interval_ms)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioInterval, audio_id.c_str(), interval_ms);
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceSetMonitorType(const std::string& audio_id, const AUDIO_MONITOR_TYPE type)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool    ok = false;
    int32_t t = MapAudioDeviceMonitorType2SDK(type, &ok);
    auto    success = call_helper_.SyncCall<bool>(g_sdk_api.SetAudioMonitorType, audio_id.c_str(), t);
    return success;
}

bool MediaSDKControllerImplV2::AudioSourceGetMonitorType(const std::string& audio_id, AUDIO_MONITOR_TYPE* type)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolInt32>(g_sdk_api.GetAudioMonitorType, audio_id.c_str());
    if (ret.success && type)
        *type = MapAudioDeviceMonitorType2Native(ret.value, NULL);
    return ret.success;
}

bool MediaSDKControllerImplV2::AudioSourceGetPerformance(const std::string& audio_id, AUDIO_PERFORMANCE_INFO* info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::AudioSourceGetPerformance)
    mediasdk::MSAudioPerformance ret;
    g_sdk_api.GetAudioInputPerformance(audio_id.c_str(), ret);
    if (info)
    {
        info->valid = ret.valid;
        info->resetTimes = ret.reset_times;
        info->cost = ret.cost;
        info->offset = ret.offset;
    }
    return true;
}

bool MediaSDKControllerImplV2::AudioSourceSetRenderDeviceID(const std::string& render_device_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    g_sdk_api.SetGlobalRenderDeviceID(render_device_id.c_str());
    return true;
}

bool MediaSDKControllerImplV2::LyraxEngineCreateAudioSource(const AUDIO_INFO& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::string audio_id = "";
    Util::NumToString(param.id, &audio_id);
    mediasdk::CreateAudioParams audio_params{};
    uint32_t                    track_ids = param.audioTrack;
    audio_params.track_id = 0;
    bool res = GetFirstAudioTrackOfTrackIds(track_ids, audio_params.track_id);
    if (!res)
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineCreateAudioSource] GetFirstAudioTrackOfTrackIds error! param.audio_id = " << audio_id << " param.audio_track_ids = " << param.audioTrack;

    if (audio_params.track_id > 0)
        audio_id_map_[audio_id] = 1 << (audio_params.track_id - 1);

    audio_params.plugin_name = wasapi_audio_source::GetPluginName();
    nlohmann::json json_params;
    json_params["device_id"] = param.device.id;
    json_params["device_name"] = param.device.name;
    json_params["audio_input_type"] = mediasdk::kAudioInputMicrophone;

    WAS_AUDIO was = std::get<WAS_AUDIO>(param.audio);
    json_params["bypass_system_enhancement_mode"] = was.sysEnhancementMode;
    json_params["mic_input_level"] = was.micInputLevel;
    json_params["mic_boost_level"] = was.micBoostLevel;
    for (const auto& part_name : was.microphoneBoostPartnames)
    {
        json_params["mic_boost_part_names"].push_back(part_name);
    }
    audio_params.json_params = mediasdk::MediaSDKString(json_params.dump().c_str());
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.CreateLyraxAudioInput, audio_id.c_str(), audio_params);
    if (!ret.success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineCreateAudioSource] error";
        return ret.success;
    }

    bool success = AudioSourceSetTracks(audio_id, param.audioTrack);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineCreateAudioSource] AudioSourceSetTracks error! audio_track_ids = " << track_ids;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::LyraxEngineSetAudioRefId(const std::string& audio_id, const std::string& ref_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.SetAudioInputReferenceId, audio_id.c_str(), ref_id.c_str());

    if (!ret.success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineSetAudioRefId] error";
    return ret.success;
}

bool MediaSDKControllerImplV2::LyraxEngineSetAECOption(const std::string& audio_id, bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.SetAudioInputAECOption, audio_id.c_str(), enable);

    if (!ret.success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineSetAECOption] error";
    return ret.success;
}

bool MediaSDKControllerImplV2::LyraxEngineSetANSOption(const std::string& audio_id, AUDIO_ANS_OPTION option)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    int  level = MapANSOption2SDK(option, &success);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineSetANSOption] error";
        return false;
    }

    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.SetAudioInputANSOption, audio_id.c_str(), level);
    if (!ret.success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineSetANSOption] error";

    return ret.success;
}

bool MediaSDKControllerImplV2::LyraxEngineSetAGCOption(const std::string& audio_id, int option)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.SetAudioInputRawDataOption, audio_id.c_str(), option);

    if (!ret.success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::SetAudioInputRawDataOption] error";
    return ret.success;
}

bool MediaSDKControllerImplV2::LyraxEngineEnableAudioInputEchoDetection(const std::string& audio_id, const int interval)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;

    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.EnableAudioInputEchoDetection, audio_id.c_str(), interval);
    if (!ret.success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineEnableAudioInputEchoDetection] error";

    return ret.success;
}

bool MediaSDKControllerImplV2::LyraxEngineSetAudioInputRenderDeviceID(const std::string& audio_id, const char* render_device_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.SetAudioInputRenderDeviceID, audio_id.c_str(), render_device_id);

    if (!ret.success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::LyraxEngineSetAudioInputRenderDeviceID] error";
    return ret.success;
}

nlohmann::json MediaSDKControllerImplV2::MapAudioCodecParam(const std::string& codec_id, const AUDIO_CODEC_PARAM& param)
{
    nlohmann::json audio_config;
    audio_config["id"] = codec_id;
    audio_config["name"] = MapEncoderName2SDK(param.codecName);
    audio_config["bitrate"] = param.bitrate;
	if (param.aacProfile.has_value())
	{
		bool ok = false;
		std::string profile = MapAudioAACProfile2SDK(param.aacProfile.value(), &ok);
		if (!ok)
		{
			LOG(ERROR) << "[MediaSDKControllerImplV2::MapAudioCodecParam] MapAudioAACProfile failed";
			audio_config["profile"] = "";
		}
		else
		{
			audio_config["profile"] = profile;
		}
	}
    return audio_config;
}

nlohmann::json MediaSDKControllerImplV2::MapVideoCodecParam(const std::string& codec_id, const VIDEO_CODEC_PARAM& param)
{
    nlohmann::json video_config;
    if (!param.codec_param_json.empty())
    {
        try
        {
            video_config = nlohmann::json::parse(param.codec_param_json);
        }
        catch (const std::exception& e)
        {
            ;
        }
    }

    video_config["id"] = codec_id;
    auto encoderName = MapEncoderName2SDK(param.codecName);
    video_config["name"] = encoderName;
    video_config["bitrate"] = param.bitrate;
    
    return video_config;
}

nlohmann::json MediaSDKControllerImplV2::MapRecordSourceConfig(const OUTPUT_INFO& param)
{
    nlohmann::json record_source_config_list_json;
    nlohmann::json record_source_config_json;
    auto           typeName = MapStreamType2SDK(param.streamInfo.type);
    record_source_config_json["name"] = typeName;
    {
        nlohmann::json config_json;
        config_json["path"] = param.streamInfo.url;
        try
        {
            std::filesystem::path filePath = param.streamInfo.url;
            std::string           extension = filePath.extension().string();
            std::transform(extension.begin(), extension.end(), extension.begin(), [](unsigned char c) { return std::tolower(c); });
            if (!extension.empty())
            {
                extension = extension.substr(1);
            }
            config_json["format"] = extension;
        }
        catch (...)
        {
            LOG(ERROR) << "record file path does not exist extension!!";
        }
        record_source_config_json["config"] = config_json;
    }
    record_source_config_list_json.push_back(record_source_config_json);
    return record_source_config_list_json;
}

nlohmann::json MediaSDKControllerImplV2::MapStreamSourceConfig(const OUTPUT_INFO& param)
{
    nlohmann::json stream_source_config_list_json;

    nlohmann::json stream_source_config_json;
    auto           typeName = MapStreamType2SDK(param.streamInfo.type);
    stream_source_config_json["name"] = typeName;
    {
        nlohmann::json config_json;
        config_json["url"] = param.streamInfo.url;
        config_json["key"] = param.streamInfo.key;
        if (param.streamInfo.vpaasConfig.has_value() && !param.streamInfo.vpaasConfig.value().empty())
            config_json["extra_json"] = nlohmann::json::parse(param.streamInfo.vpaasConfig.value());
        stream_source_config_json["config"] = config_json;
        stream_source_config_json["reconnect_count"] = param.streamInfo.reconnectCnt;
        stream_source_config_json["reconnect_interval"] = param.streamInfo.reconnectTime * 1000;
        if (param.streamInfo.cipher.has_value())
        {
            stream_source_config_json["cipher"] = param.streamInfo.cipher.value();
        }
        if (param.streamInfo.plainText.has_value())
        {
            stream_source_config_json["plain_text"] = param.streamInfo.plainText.value();
        }
    }
    static std::once_flag flag;
    std::call_once(flag,
                   [this]() {
                       mediasdk::PluginInfoArray result = call_helper_.SyncCall<mediasdk::PluginInfoArray>(g_sdk_api.EnumServiceSource);
                       auto                      infos = result.ToVector();
                       hasRTMPQ_ = std::any_of(infos.begin(), infos.end(), [](const auto& it) { return it.name.ToString() == "RTMPQStreamServiceSource"; });
                   });

    if ((typeName == "RTMPQStreamServiceSource" && hasRTMPQ_) || typeName == "RTMPStreamServiceSource")
    {
        stream_source_config_list_json.push_back(stream_source_config_json);
    }

    if (!param.fallbackStreamInfo.has_value())
    {
        return stream_source_config_list_json;
    }
    nlohmann::json fallback_stream_source_config_json;
    auto&          fallback_stream_param = param.fallbackStreamInfo.value();
    fallback_stream_source_config_json["name"] = MapStreamType2SDK(fallback_stream_param.type);
    {
        nlohmann::json config_json;
        config_json["url"] = fallback_stream_param.url;
        config_json["key"] = fallback_stream_param.key;
        if (fallback_stream_param.vpaasConfig.has_value() && !fallback_stream_param.vpaasConfig.value().empty())
        {
            config_json["extra_json"] = nlohmann::json::parse(fallback_stream_param.vpaasConfig.value());
        }
        fallback_stream_source_config_json["config"] = config_json;
    }

    fallback_stream_source_config_json["reconnect_count"] = fallback_stream_param.reconnectCnt;
    fallback_stream_source_config_json["reconnect_interval"] = fallback_stream_param.reconnectTime * 1000;
    if (fallback_stream_param.cipher.has_value())
    {
        fallback_stream_source_config_json["cipher"] = fallback_stream_param.cipher.value();
    }
    if (fallback_stream_param.plainText.has_value())
    {
        fallback_stream_source_config_json["plain_text"] = fallback_stream_param.plainText.value();
    }
    stream_source_config_list_json.push_back(fallback_stream_source_config_json);

    return stream_source_config_list_json;
}

nlohmann::json MediaSDKControllerImplV2::MapStreamCdnList(const OUTPUT_INFO& param)
{
    nlohmann::json cdn_list_json;
    if (param.streamInfo.cdnIps.has_value())
    {
        std::stringstream ss(param.streamInfo.cdnIps.value());
        std::string       token;
        while (std::getline(ss, token, ';'))
        {
            cdn_list_json.push_back(token);
        }
    }
    return cdn_list_json;
}

mediasdk::StreamParams MediaSDKControllerImplV2::MapStartStreamParam(const OUTPUT_INFO& param)
{
    nlohmann::json json_root;
    json_root["id"] = param.streamID;
    json_root["sink_id"] = param.videoModel;
    json_root["track_id"] = param.audioTrack;
    json_root["audio_encoder_config"] = MapAudioCodecParam(param.codecID, param.audioCodec);
    json_root["video_encoder_config"] = MapVideoCodecParam(param.codecID, param.videoCodec);
    nlohmann::json json_orient;
    json_orient["orientation"] = param.flvMetadataOrientation;
    json_root["custom_flv_metadata"] = json_orient;

    auto typeName = MapStreamType2SDK(param.streamInfo.type);
    if (typeName == "RecordStreamServiceSource")
        json_root["stream_source_config_list"] = MapRecordSourceConfig(param);
    else
        json_root["stream_source_config_list"] = MapStreamSourceConfig(param);
    json_root["cdn_list"] = MapStreamCdnList(param);
    if (param.streamInfo.delay.has_value())
        json_root["delay_ms"] = param.streamInfo.delay.value() * 1000;

    {
        nlohmann::json abr_strategy_json;
        abr_strategy_json["strategy"] = "common";
        abr_strategy_json["min_bitrate"] = param.abrStrategy.minBitrate;
        abr_strategy_json["offset"] = param.abrStrategy.offset;
        json_root["abr_strategy"] = abr_strategy_json;
    }
    json_root["speed_test_target_bitrate"] = param.videoCodec.bitrate;
    return json_root.dump();
}

mediasdk::StreamParams MediaSDKControllerImplV2::MapStartStreamContext(const OUTPUT_INFO& info)
{
	nlohmann::json json_root;
	json_root["id"] = info.streamID;
	json_root["reason"] = static_cast<uint32_t>(info.reason);
	json_root["url"] = info.streamInfo.url;
	json_root["key"] = info.streamInfo.key;
	return json_root.dump();
}

bool MediaSDKControllerImplV2::StartStream(const std::vector<OUTPUT_INFO>& params)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    for (const auto& param : params)
    {
        mediasdk::StreamParams streamContext = MapStartStreamContext(param);
        g_sdk_api.StartPushStream(streamContext);

        mediasdk::StreamParams streamParams = MapStartStreamParam(param);
        {
            std::unique_lock<std::mutex> lock(g_mutex_);
            g_startStreamType[param.streamID] = param.streamInfo.type;
        }
        g_sdk_api.StartStream(streamParams, sdk_callback_mgr_.get());
    }
    return true;
}

bool MediaSDKControllerImplV2::StopStream(const std::vector<STOP_STREAM_PARAM>& stream_params)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    for (auto& param : stream_params)
    {
        nlohmann::json json_root;
        json_root["id"] = param.streamID;
        json_root["reason"] = (int)param.reason;
        g_sdk_api.StopPushStream(json_root.dump());

        g_sdk_api.StopStream(param.streamID.c_str());
    }
    return true;
}

bool MediaSDKControllerImplV2::StreamSetRoomID(const std::string& room_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::StreamSetRoomID)
    auto result = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.SetLyraxLiveRoomId, room_id.c_str());
    if (!result.success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::StreamSetRoomID] SetLyraxLiveRoomId error, room_id: " << room_id;

    return result.success;
}

static StreamSEIBuilder g_seiBuilder;

bool MediaSDKControllerImplV2::SetIFrameSEI(const std::vector<SEI_INFO>& params)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::SetIFrameSEI)
    bool                        success = false;
    std::map<std::string, bool> updateStreamID;
    for (int i = 0; i < params.size(); i++)
    {
        auto curParam = params[i];
        if (curParam.seiOpt == SEI_OPT::SEI_OPT_UPDATE)
        {
            if (curParam.seiType == SEI_VALUE_TYPE::SEI_JSON)
            {
                g_seiBuilder.SetJson(curParam.streamID, curParam.key, curParam.sei);
            }
            else if (curParam.seiType == SEI_VALUE_TYPE::SEI_STRING)
            {
                g_seiBuilder.SetString(curParam.streamID, curParam.key, curParam.sei);
            }
            else if (curParam.seiType == SEI_VALUE_TYPE::SEI_INTEGER)
            {
                g_seiBuilder.SetInt(curParam.streamID, curParam.key, std::stoi(curParam.sei));
            }
            else if (curParam.seiType == SEI_VALUE_TYPE::SEI_FLOAT)
            {
                g_seiBuilder.SetFloat(curParam.streamID, curParam.key, std::stof(curParam.sei));
            }
            updateStreamID[curParam.streamID] = 0 | curParam.flushImmediately;
        }
        else if (curParam.seiOpt == SEI_OPT::SEI_OPT_REMOVE)
        {
            g_seiBuilder.Remove(curParam.streamID, curParam.key);
            updateStreamID[curParam.streamID] = 0 | curParam.flushImmediately;
        }
    }
    for (auto itor = updateStreamID.begin(); itor != updateStreamID.end(); itor++)
    {
        auto           sei_str = g_seiBuilder.ToJson(itor->first);
        nlohmann::json json_root;
        json_root["stream_id"] = itor->first;
        {
            nlohmann::json json_config;
            if (itor->second == true)
            {
                json_config["type"] = 5; // KTypeLoopAfterOnce;
            }
            else
            {
                json_config["type"] = 2; // kTypeLoopI;
            }
            json_config["value"] = sei_str;
            json_root["conf"] = json_config;
        }
        g_sdk_api.SetStreamSEI(json_root.dump().c_str());
    }
    return true;
}

bool MediaSDKControllerImplV2::EnumVideoEncoders(std::vector<VIDEO_ENCODER_INFO>* video_encoder_info_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::PluginInfoArray result = call_helper_.SyncCall<mediasdk::PluginInfoArray>(g_sdk_api.EnumEncoderSource);
    auto                      infos = result.ToVector();
    bool                      success = false;
    for (uint32_t i = 0; i < infos.size(); ++i)
    {
        VIDEO_ENCODER_INFO video_encoder_info;
        video_encoder_info.name = MapEncoderName2Native(infos[i].name.ToString());
        auto desc_str = infos[i].desc.ToString();
        try
        {
            nlohmann::json desc_json = nlohmann::json::parse(desc_str);
            video_encoder_info.hardware = !!desc_json.value("hardware_accelerate", 0);
            video_encoder_info.codec = desc_json.value("encoder_type", 0);
            success = true;
        }
        catch (...)
        {
            LOG(ERROR) << "[MediaSDKControllerImplV2::EnumVideoEncoders] parse json error!";
            break;
        }
        if (video_encoder_info_list)
            video_encoder_info_list->push_back(video_encoder_info);
    }
    return success;
}

bool MediaSDKControllerImplV2::SetVideoSetting(const SetVideoSettingParam& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (param.track_id_list.size() != param.video_param_list.size())
        return false;

    bool isSuccess = true;
    for (size_t i = 0; i < param.track_id_list.size(); ++i)
    {
        const int32_t track_id = param.track_id_list[i];
        const auto&   video_param = param.video_param_list[i];
        g_video_fps = video_param.fps;
        auto ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.UpdateModelFPS, track_id, video_param.fps);
        if (!ret.success || !ret.value)
        {
            isSuccess = false;
            break;
        }
        mediasdk::MSSize msSize = {video_param.output_size_width, video_param.output_size_height};
        ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.UpdateModelOutputSize, track_id, msSize);
        if (!ret.success || !ret.value)
        {
            isSuccess = false;
            break;
        }
        bool ok = false;
        auto colorSpace = MapColorSpace2SDK(video_param.color_space, &ok);
        if (!ok)
        {
            LOG(ERROR) << "ColorSpaceEnum transfer to mediasdk::ColorSpace failed! video_param.color_space = " << static_cast<int>(video_param.color_space);
            isSuccess = false;
            break;
        }
        auto colorRange = MapColorRange2SDK(video_param.color_range, &ok);
        if (!ok)
        {
            LOG(ERROR) << "ColorRangeEnum transfer to mediasdk::VideoRange failed! video_param.color_range = " << static_cast<int>(video_param.color_range);
            isSuccess = false;
            break;
        }
        ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.UpdateModelColorSpaceAndVideoRange, track_id, colorSpace, colorRange);
        if (!ret.success || !ret.value)
        {
            isSuccess = false;
            break;
        }
    }
    return isSuccess;
}

bool MediaSDKControllerImplV2::StreamSetAbrConfig(const std::string& stream_id, uint32_t offset)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::StreamSetAbrConfig)
    g_sdk_api.UpdateStreamAbrOffset(stream_id.c_str(), static_cast<int>(offset));
    return true;
}

bool MediaSDKControllerImplV2::IsStreamInProcess(const std::string& streamID, bool* is_streaming)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool ret = call_helper_.SyncCall<bool>(g_sdk_api.IsStreamInProcess, streamID.c_str());
    if (is_streaming)
        *is_streaming = ret;
    return true;
}

bool MediaSDKControllerImplV2::EncoderGetInfo(const std::string& avcodec_tag, int32_t* error_code, VIDEO_CODEC_PARAM* video_codec_param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    // error_code 见 MeidaSDKController.h中注释
    mediasdk::EncoderBitrate ret = call_helper_.SyncCall<mediasdk::EncoderBitrate>(g_sdk_api.VideoEncoderTargetBitrate, avcodec_tag.c_str());
    if (error_code)
        *error_code = static_cast<int32_t>(ret.error_code);
    if (video_codec_param)
        video_codec_param->bitrate = static_cast<int32_t>(ret.bitrate);
    return true;
}

bool MediaSDKControllerImplV2::BwProbeStartStream(const OUTPUT_INFO& param, bool* already_running)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    if (already_running_)
    {
        return false;
    }
    mediasdk::StreamParams streamParams = MapStartStreamParam(param);
    {
        std::unique_lock<std::mutex> lock(g_mutex_);
        g_startStreamType[param.streamID] = param.streamInfo.type;
    }
    g_sdk_api.StartSpeedTestStream(streamParams, sdk_callback_mgr_.get());
    if (already_running)
    {
        *already_running = already_running_;
    }
    already_running_ = true;
    return true;
}

bool MediaSDKControllerImplV2::BwProbeStopStream(const std::string& stream_id, bool* is_fallbacking)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    g_sdk_api.StopSpeedTestStream(stream_id.c_str());
    if (is_fallbacking)
        *is_fallbacking = false; // 重构后不会有降级导致stop失败的情况
    already_running_ = false;
    return true;
}

bool MediaSDKControllerImplV2::CreateModel(const uint32_t video_model, int width, int height, int fps, HWND hwnd_parent)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::ModelParams params;
    params.output_size.cx = width;
    params.output_size.cy = height;
    params.fps = fps;
    params.color_space = mediasdk::kColorSpaceBT601;
    params.video_range = mediasdk::kVideoRangeFull;
    params.hwnd_parent = hwnd_parent;
    params.window_rect.x = 0;
    params.window_rect.y = 0;
    params.window_rect.cx = 800;
    params.window_rect.cy = 600;
    params.auto_top_zorder = true;
    params.show_window = true;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateModel, video_model, params);
    if (success)
    {
        model_track_id_.insert(video_model);
        SetPreviewDefaultSetting(video_model);
    }

    return success;
}

bool MediaSDKControllerImplV2::RemoveModel(const uint32_t video_model)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.RemoveModel, video_model);
    if (success)
    {
        model_track_id_.erase(video_model);
    }
    return success;
}

bool MediaSDKControllerImplV2::SetModelActive(const uint32_t video_model, bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetVideoModelActive, video_model, enable);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::SetModelActive] SetVideoModelActive failed";
    }
    return success;
}

bool MediaSDKControllerImplV2::GetModelActive(const uint32_t video_model, bool* enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::ResultBoolBool result = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.GetVideoModelActive, video_model);
    if (result.success && enable)
    {
        *enable = result.value;
    }
    else
    {
        LOG(ERROR) << "[ediaSDKControllerImplV2::GetModelActive] GetVideoModelActive failed";
    }
    return result.success;
}

bool MediaSDKControllerImplV2::PreviewWindowSetPosition(const int32_t track_id, const Gdiplus::RectF& view_rect, const Gdiplus::RectF& view_rect_layout)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSRect r;
    r.x = view_rect.X - (view_rect_layout.Width - view_rect.Width + 1) / 2;
    r.y = view_rect.Y - (view_rect_layout.Height - view_rect.Height + 1) / 2;
    r.cx = view_rect_layout.Width;
    r.cy = view_rect_layout.Height;
    mediasdk::MSClip clip = {(view_rect_layout.Width - view_rect.Width) / 2, 0, (view_rect_layout.Width - view_rect.Width) / 2, 0};

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetAndClipPreviewPosition, track_id, r, clip);

    return success;
}

bool MediaSDKControllerImplV2::PreviewWindowEnableShow(const int32_t track_id, bool show)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;

    if (show)
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.ShowPreviewWindow, track_id);
    }
    else
    {
        success = call_helper_.SyncCall<bool>(g_sdk_api.HidePreviewWindow, track_id);
    }
    return success;
}

bool MediaSDKControllerImplV2::PreviewWindowEnableDraw(const int32_t track_id, const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.EnablePreviewWithVideoModelId, track_id, enable);
    return success;
}

bool MediaSDKControllerImplV2::PreviewWindowIsEnableDraw(const int32_t track_id, bool* enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::ResultBoolBool ret = call_helper_.SyncCall<mediasdk::ResultBoolBool>(g_sdk_api.IsPreviewEnableWithVideoModelId, track_id);
    if (ret.success)
    {
        if (enable)
            *enable = ret.value;
    }

    return ret.success;
}

bool MediaSDKControllerImplV2::PreviewWindowEnableInteract(const int32_t track_id, const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.EnableTrack, track_id, enable);
    return success;
}

bool MediaSDKControllerImplV2::PreviewWindowOpenProjector(const std::string& preview_id, const int32_t track_id, const int64_t win_id, const int32_t x, const int32_t y, const int32_t width, const int32_t height, const PREVIEW_PARAMS& params)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSRect rect;
    rect.x = x;
    rect.y = y;
    rect.cx = width;
    rect.cy = height;
    mediasdk::WndParams wndParams = { params.isPopUp, params.topBorderRadius, params.bottomBorderRadius, params.opacity };
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateProjector, preview_id.c_str(), win_id, track_id, rect, wndParams);
    return success;
}

bool MediaSDKControllerImplV2::PreviewWindowCloseProjector(const std::string& preview_id, const int32_t track_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CloseProjector, preview_id.c_str(), track_id);
    return success;
}

bool MediaSDKControllerImplV2::PreviewWindowSaveAsImage(const int32_t track_id, const IMAGE_FORMAT fmt, const std::string& filepath)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    const std::map<IMAGE_FORMAT, mediasdk::ImageFileFormat> mp = {{IMAGE_FORMAT::IMAGE_FORMAT_BMP, mediasdk::kImageFileFormatBMP},
                                                                     {IMAGE_FORMAT::IMAGE_FORMAT_JPG, mediasdk::kImageFileFormatJPG},
                                                                     {IMAGE_FORMAT::IMAGE_FORMAT_PNG, mediasdk::kImageFileFormatPNG},
                                                                     {IMAGE_FORMAT::IMAGE_FORMAT_TIFF, mediasdk::kImageFileFormatTIFF},
                                                                     {IMAGE_FORMAT::IMAGE_FORMAT_GIF, mediasdk::kImageFileFormatGIF},
                                                                     {IMAGE_FORMAT::IMAGE_FORMAT_WMP, mediasdk::kImageFileFormatWMP}};
    if (mp.find(fmt) == mp.end())
        return false;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.OutputThumbnailSaveAs, track_id, filepath.c_str(), mp.at(fmt));
    return success;
}

bool MediaSDKControllerImplV2::SetProjectorWndParams(const std::string& preview_id, const int32_t track_id, const PREVIEW_PARAMS& params)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::WndParams sdkParams = {true, params.topBorderRadius, params.bottomBorderRadius, params.opacity};
    bool                success = call_helper_.SyncCall<bool>(g_sdk_api.SetProjectorWndParams, preview_id.c_str(), track_id, sdkParams);
    return success;
}

bool MediaSDKControllerImplV2::SetProjectorPosition(const std::string& preview_id, const int32_t track_id, const Gdiplus::RectF& rect)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSRect region = {rect.X, rect.Y, rect.Width, rect.Height};
    bool             success = true;
    success = call_helper_.SyncCall<bool>(g_sdk_api.SetProjectorPosition, preview_id.c_str(), track_id, region);
    return success;
}

bool MediaSDKControllerImplV2::StartLayerPreview(const std::string& layer_id, const std::string& preview_id, const int64_t parent_win_id, const LAYER_PREVIEW& setting, const PREVIEW_PARAMS& params)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    LOG(INFO) << "[MediaSDKControllerImplV2::StartLayerPreview] layer_id: " << layer_id << ", preview_id: " << preview_id << ", parent_hwnd: " << parent_win_id << ", preview_setting : " << setting.toString();
    mediasdk::MSRect region = {setting.transform.translate.X, setting.transform.translate.Y, setting.size.Width, setting.size.Height};
    mediasdk::WndParams wndParams = { params.isPopUp, params.topBorderRadius, params.bottomBorderRadius, params.opacity };
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.StartCanvasItemPreview, layer_id.c_str(), preview_id.c_str(), static_cast<uint64_t>(parent_win_id), region,
                                               setting.fillType, setting.transform.hFlip, setting.transform.vFlip, setting.transform.angle, setting.bkColor, wndParams);
    if (!success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::StartLayerPreview] StartCanvasItemPreview failed";

    return success;
}

bool MediaSDKControllerImplV2::StopLayerPreview(const std::string& preview_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    LOG(INFO) << "[MediaSDKControllerImplV2::StopLayerPreview] preview_id: " << preview_id;
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.StopCanvasItemPreview, preview_id.c_str());
	if (!success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::StopLayerPreview] StopCanvasItemPreview failed";

    return success;
}

bool MediaSDKControllerImplV2::SetLayerPreviewSetting(const std::string& preview_id, const LAYER_PREVIEW& setting)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    LOG(INFO) << "[MediaSDKControllerImplV2::SetLayerPreviewSetting] preview_id: " << preview_id << ", preview_setting: " << setting.toString();
    mediasdk::MSRect region = { setting.transform.translate.X, setting.transform.translate.Y, setting.size.Width, setting.size.Height };
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemPreviewPos, preview_id.c_str(), region);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::SetLayerPreviewSetting] SetCanvasItemPreviewPos failed";
        return false;
    }

    success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemPreviewFlipH, preview_id.c_str(), setting.transform.hFlip);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::SetLayerPreviewSetting] SetCanvasItemPreviewFlipH failed";
        return false;
    }

    success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemPreviewFlipV, preview_id.c_str(), setting.transform.vFlip);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::SetLayerPreviewSetting] SetCanvasItemPreviewFlipV failed";
        return false;
    }

    success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemPreviewRotate, preview_id.c_str(), setting.transform.angle);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::SetLayerPreviewSetting] SetCanvasItemPreviewRotate failed";
        return false;
    }

    return success;
}

bool MediaSDKControllerImplV2::SetLayerPreviewParams(const std::string& visual_preview_id, const PREVIEW_PARAMS& params)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::WndParams sdkParams = { true, params.topBorderRadius, params.bottomBorderRadius, params.opacity };
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemPreviewParams, visual_preview_id.c_str(), sdkParams);
    return success;
}

bool MediaSDKControllerImplV2::RTCControllerStart(const RTC_LINK& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    rtc_video_track_id_ = param.videoModel;
    rtc_audio_track_id_ = param.audioTrack;
    rtc_fps_ = param.clipAreaInfo.fps;

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.RegisterRTCEventObserver, sdk_callback_mgr_.get());
    if (!success)
        return false;
    if (rtc_helper_)
        success = rtc_helper_->StartRTC(param);
    return success;
}

bool MediaSDKControllerImplV2::RTCControllerStop()
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	int ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalAudio, mediasdk::StreamIndex::kStreamIndexMain, false, 2);
	if (ret != 0)
	{
		LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStop] EnableLocalAudio error, code = " << ret;
		return false;
	}
    ret = call_helper_.SyncCall<int>(g_sdk_api.LeaveRoom);
    if (ret != 0)
    {
        LOG(ERROR) << "[RTCControllerStop] Step1: LeaveRoom error, code = " << ret;
        return false;
    }

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyEngine);
    if (!success)
    {
        LOG(ERROR) << "[RTCControllerStop] Step2: DestroyEngine error";
        return false;
    }

    success = call_helper_.SyncCall<bool>(g_sdk_api.UnregisterRTCEventObserver, sdk_callback_mgr_.get());
    if (!success)
    {
        LOG(ERROR) << "[RTCControllerStop] Step3: UnregisterRTCEventObserver error";
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerStartScreenShare(UINT32 videoModelID, const CLIP_AREA_INFO& info, bool enableAudio, UINT32 audioTrack)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    uint32_t audio_track_ids = audioTrack;
    uint32_t cur_audio_track_id = 0;
    bool     res = GetFirstAudioTrackOfTrackIds(audio_track_ids, cur_audio_track_id);
    if (!res)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStartScreenShare] GetFirstAudioTrackOfTrackIds error! audioTrack = " << audioTrack;
    }
    screenShare_audio_track_ids_ = cur_audio_track_id;
    screenShare_enable_audio_ = cur_audio_track_id;

    LOG(INFO) << "[MediaSDKControllerImplV2::RTCControllerStartScreenShare] EnableLocalAudio enableAudio = " << enableAudio << "  audioTrack = " << audioTrack;
    int ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalAudio, mediasdk::StreamIndex::kStreamIndexScreen, enableAudio, cur_audio_track_id);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStartScreenShare] EnableLocalAudio error, code = " << ret << " cur_audio_track_id = " << cur_audio_track_id;
        return false;
    }

    LOG(INFO) << "[MediaSDKControllerImplV2::RTCControllerStartScreenShare] EnableLocalVideo " << " videoModelID = " << videoModelID;
    ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalVideo, mediasdk::StreamIndex::kStreamIndexScreen, true, videoModelID);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStartScreenShare] EnableLocalVideo error, code = " << ret;
        return false;
    }
    bytertc::MediaStreamType streamType = enableAudio ? bytertc::MediaStreamType::kMediaStreamTypeBoth : bytertc::MediaStreamType::kMediaStreamTypeVideo;
    ret = call_helper_.SyncCall<int>(g_sdk_api.PublishStream, mediasdk::StreamIndex::kStreamIndexScreen, streamType);
    if (ret != 0)
    {
        LOG(ERROR) << " [MediaSDKControllerImplV2::RTCControllerStartScreenShare] PublishStream error! code = " << ret;
        return false;
    }
    nlohmann::json json_params;
    json_params["framerate"] = info.fps == 0 ? g_video_fps : info.fps;
    json_params["region"] = { info.clip.X, info.clip.Y, info.clip.Width, info.clip.Height };
    json_params["scale"] = info.scale;
    json_params["max_bitrate"] = info.maxBitrate;
    json_params["min_bitrate"] = info.minBitrate;
    json_params["enable_simulcast"] = false;

    LOG(INFO) << "[MediaSDKControllerImplV2::RTCControllerStartScreenShare] UpdateCropAndScale  json_params.dump().c_str() = " << json_params.dump();
    ret = call_helper_.SyncCall<int>(g_sdk_api.UpdateCropAndScale, mediasdk::StreamIndex::kStreamIndexScreen, json_params.dump().c_str());
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStartScreenShare] UpdateCropAndScale error!";
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerStopScreenShare(const int32_t track_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    int ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalAudio, mediasdk::StreamIndex::kStreamIndexScreen, false, screenShare_audio_track_ids_);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStopScreenShare] EnableLocalAudio error, code = " << ret;
        return false;
    }
    ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalVideo, mediasdk::StreamIndex::kStreamIndexScreen, false, track_id);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStopScreenShare] EnableLocalVideo error, code = " << ret;
        return false;
    }
    bytertc::MediaStreamType streamType = screenShare_enable_audio_ ? bytertc::MediaStreamType::kMediaStreamTypeBoth : bytertc::MediaStreamType::kMediaStreamTypeVideo;
    ret = call_helper_.SyncCall<int>(g_sdk_api.UnPublishStream, mediasdk::StreamIndex::kStreamIndexScreen, streamType);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStopScreenShare] UnPublishStream error!, code = " << ret;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerUpdateScreenShareClipInfo(const CLIP_AREA_INFO& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_params;
    json_params["framerate"] = param.fps == 0 ? g_video_fps : param.fps;
    json_params["region"] = {param.clip.X, param.clip.Y, param.clip.Width, param.clip.Height};
    json_params["scale"] = param.scale;
    json_params["max_bitrate"] = param.maxBitrate;
    json_params["min_bitrate"] = param.minBitrate;
    json_params["enable_simulcast"] = false;

    LOG(INFO) << "[MediaSDKControllerImplV2::RTCControllerUpdateScreenShareClipInfo] UpdateCropAndScale  json_params.dump().c_str() = " << json_params.dump();
    int ret = call_helper_.SyncCall<int>(g_sdk_api.UpdateCropAndScale, mediasdk::StreamIndex::kStreamIndexScreen, json_params.dump().c_str());
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerStartScreenShare] UpdateCropAndScale error!";
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerSetAudioOutput(const std::string& device_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    int ret = call_helper_.SyncCall<int>(g_sdk_api.SetAudioPlaybackDevice, device_id.c_str());
    if (ret != 0)
    {
        LOG(ERROR) << "SetAudioPlaybackDevice error, code = " << ret;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerSendRTCUserMessage(const std::string& uid, const std::string& msg)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    int message_id = call_helper_.SyncCall<int>(g_sdk_api.SendUserMessage, uid.c_str(), msg.c_str());
    if (message_id == -1)
    {
        LOG(ERROR) << "SendUserMessage error, code = " << message_id;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerSendRTCRoomMessage(const std::string& msg)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    int message_id = call_helper_.SyncCall<int>(g_sdk_api.SendRoomMessage, msg.c_str());
    if (message_id == -1)
    {
        LOG(ERROR) << "SendRoomMessage error, code = " << message_id;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerEnableLocalAudio(const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerEnableLocalAudio] enable = " << enable;
    int ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalAudio, mediasdk::StreamIndex::kStreamIndexMain, enable, rtc_audio_track_id_);
    if (ret != 0)
    {
        LOG(ERROR) << "EnableLocalAudio error, code = " << ret;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerEnableLocalVideo(const bool enable)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    int ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalVideo, bytertc::kStreamIndexMain, enable, rtc_video_track_id_);
    if (ret != 0)
    {
        LOG(ERROR) << "EnableLocalVideo error, code = " << ret;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerSetAudioPropertiesReport(const int32_t interval)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    int ret = call_helper_.SyncCall<int>(g_sdk_api.EnableAudioPropertiesReport, interval);
    if (ret != 0)
    {
        LOG(ERROR) << "EnableAudioPropertiesReport error! code = " << ret;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerMuteRemoteAudio(const std::string& uid, const int32_t stream_index, const bool mute)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = true;
    int  ret = 0;
    if (mute)
    {
        ret = call_helper_.SyncCall<int>(g_sdk_api.UnSubscribeStream, uid.c_str(), stream_index, bytertc::kMediaStreamTypeAudio);
        if (ret != 0)
        {
            LOG(ERROR) << "UnSubscribeStream error! code = " << ret;
            success = false;
        }
    }
    else
    {
        ret = call_helper_.SyncCall<int>(g_sdk_api.SubscribeStream, uid.c_str(), stream_index, bytertc::kMediaStreamTypeAudio);
        if (ret != 0)
        {
            LOG(ERROR) << "SubscribeStream error! code = " << ret;
            success = false;
        }
    }
    return success;
}

bool MediaSDKControllerImplV2::RTCControllerPublishVideoStream(MEDIA_STREAM_TYPE type)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    auto media_type = MapRTCControllerMediaStreamType2SDK(type, &success);

    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerPublishVideoStream] MapRTCControllerMediaStreamType failed, type: " << static_cast<uint32_t>(type);
        return false;
    }

    int ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalVideo, mediasdk::StreamIndex::kStreamIndexMain, true, rtc_video_track_id_);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerPublishVideoStream] EnableLocalVideo error, code = " << ret;
        return false;
    }
    ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalAudio, mediasdk::StreamIndex::kStreamIndexMain, true, rtc_audio_track_id_);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerPublishVideoStream] EnableLocalAudio error, code = " << ret;
        return false;
    }
    ret = call_helper_.SyncCall<int>(g_sdk_api.PublishStream, bytertc::kStreamIndexMain, media_type);
    if (ret != 0)
    {
        LOG(ERROR) << "PublishStream error! code = " << ret;
        return false;
    }

    return true;
}

bool MediaSDKControllerImplV2::RTCControllerUnPublishVideoStream(MEDIA_STREAM_TYPE type)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    auto media_type = MapRTCControllerMediaStreamType2SDK(type, &success);
    if (!success)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerUnPublishVideoStream] MapRTCControllerMediaStreamType2SDK failed, type: " << static_cast<uint32_t>(type);
        return false;
    }

    int ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalVideo, mediasdk::StreamIndex::kStreamIndexMain, false, rtc_video_track_id_);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerPublishVideoStream] EnableLocalVideo error, code = " << ret;
        return false;
    }
    ret = call_helper_.SyncCall<int>(g_sdk_api.EnableLocalAudio, mediasdk::StreamIndex::kStreamIndexMain, false, rtc_audio_track_id_);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerPublishVideoStream] EnableLocalAudio error, code = " << ret;
        return false;
    }
    ret = call_helper_.SyncCall<int>(g_sdk_api.UnPublishStream, bytertc::kStreamIndexMain, media_type);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerUnPublishVideoStream] UnPublishStream error!, code = " << ret;
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerStartLiveTranscoding(const std::string& agrs)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    if (rtc_helper_)
        success = rtc_helper_->StartLiveTranscoding(agrs);
    return success;
}

bool MediaSDKControllerImplV2::RTCControllerUpdateLiveTranscoding(const std::string& agrs)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    if (rtc_helper_)
        success = rtc_helper_->UpdateLiveTranscoding(agrs);
    return success;
}

bool MediaSDKControllerImplV2::RTCControllerStopLiveTranscoding(const std::string& agrs)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    if (rtc_helper_)
        success = rtc_helper_->StopLiveTranscoding(agrs);
    return success;
}

bool MediaSDKControllerImplV2::RTCControllerStartForwardStreamToRooms(const std::vector<ROOM_INFO>& info_list, int32_t* ret)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    nlohmann::json json_content;
    for (const auto& in_room_info : info_list)
    {
        nlohmann::json json_item;
        json_item["room_id"] = in_room_info.roomID;
        json_item["token"] = in_room_info.token;
        json_content.push_back(json_item);
    }
    json_root["forward_config"] = json_content;
    int result = call_helper_.SyncCall<int>(g_sdk_api.StartForwardStreamToRooms, json_root.dump().c_str());
    if (ret)
        *ret = result;
    return !result;
}

bool MediaSDKControllerImplV2::RTCControllerUpdateForwardStreamToRooms(const std::vector<ROOM_INFO>& info_list, int32_t* ret)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_root;
    nlohmann::json json_content;
    for (const auto& in_room_info : info_list)
    {
        nlohmann::json json_item;
        json_item["room_id"] = in_room_info.roomID;
        json_item["token"] = in_room_info.token;
        json_content.push_back(json_item);
    }
    json_root["forward_config"] = json_content;

    int result = call_helper_.SyncCall<int>(g_sdk_api.UpdateForwardStreamToRooms, json_root.dump().c_str());
    if (ret)
        *ret = result;
    return !result;
}

bool MediaSDKControllerImplV2::RTCControllerStopForwardStreamToRooms()
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    int result = call_helper_.SyncCall<int>(g_sdk_api.StopForwardStreamToRooms);
    if (result != 0)
    {
        LOG(ERROR) << " RTCControllerStopForwardStreamToRooms error !";
        return false;
    }
    return true;
}

static float AlignToEven(float num)
{
    uint64_t x = static_cast<uint64_t>(num);
    x = (x % 2 == 0 ? x : x + 1);
    return x * 1.0f;
}

bool MediaSDKControllerImplV2::RTCControllerUpdateClipScaleInfo(const CLIP_AREA_INFO& param)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    nlohmann::json json_params;
    json_params["framerate"] = param.fps;
    json_params["region"] = { AlignToEven(param.clip.X),  AlignToEven(param.clip.Y),  AlignToEven(param.clip.Width),  AlignToEven(param.clip.Height) };
    json_params["scale"] = param.scale;
    json_params["max_bitrate"] = param.maxBitrate;
    json_params["min_bitrate"] = param.minBitrate;
    json_params["enable_simulcast"] = false;

    LOG(INFO) << "[MediaSDKControllerImplV2::RTCControllerUpdateClipScaleInfo] UpdateCropAndScale  json_params.dump().c_str() = " << json_params.dump();
    int ret = call_helper_.SyncCall<int>(g_sdk_api.UpdateCropAndScale, mediasdk::StreamIndex::kStreamIndexMain, json_params.dump().c_str());
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerUpdateClipScaleInfo] UpdateCropAndScale error!";
        return false;
    }
    return true;
}

bool MediaSDKControllerImplV2::RTCControllerSubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    auto media_type = MapRTCControllerMediaStreamType2SDK(type, &success);
    LOG(INFO) << "uid" << uid << " type = " << (int)type;
    if (!success)
    {
        LOG(ERROR) << "MapRTCControllerMediaStreamType error! type = " << (int)type;
        return false;
    }

    int ret = call_helper_.SyncCall<int>(g_sdk_api.SubscribeStream, uid.c_str(), mediasdk::StreamIndex::kStreamIndexScreen, media_type);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerSubscribeScreen] SubscribeStream error! code = " << ret;
        success = false;
    }
    return success;
}

bool MediaSDKControllerImplV2::RTCControllerUnSubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    auto media_type = MapRTCControllerMediaStreamType2SDK(type, &success);
    if (!success)
    {
        LOG(ERROR) << "MapRTCControllerMediaStreamType error! type = " << (int)type;
        return false;
    }

    int ret = call_helper_.SyncCall<int>(g_sdk_api.UnSubscribeStream, uid.c_str(), mediasdk::StreamIndex::kStreamIndexScreen, media_type);
    if (ret != 0)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::RTCControllerUnSubscribeScreen] SubscribeStream error! code = " << ret;
        success = false;
    }
    return success;
}

bool MediaSDKControllerImplV2::GetStreamActiveStatistic(const std::string& stream_id, ACTIVE_STATISTIC* info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::GetStreamActiveStatistic)
    mediasdk::StatisticInfo        retStatisticInfo = call_helper_.SyncCall<mediasdk::StatisticInfo>(g_sdk_api.GetStatisticInfo, stream_id.c_str());
    mediasdk::EncoderTsInfo        retEncoderTsInfo = call_helper_.SyncCall<mediasdk::EncoderTsInfo>(g_sdk_api.GetEncoderPtsByStreamId, stream_id.c_str());
    mediasdk::EncoderStatisticInfo retEncoderStatisticInfo;
    g_sdk_api.GetEncoderStatisticInfo(stream_id.c_str(), retEncoderStatisticInfo);
    if (info)
    {
        info->codecName = MapEncoderName2Native(retEncoderStatisticInfo.video_codec_name.ToString());
        info->videoEncFps = retEncoderStatisticInfo.video_enc_fps;
        info->videoEncErrorFps = retEncoderStatisticInfo.video_encode_err_fps;
        info->videoSendFps = retStatisticInfo.video_send_fps;
        info->totalVideoPackets = retStatisticInfo.total_video_packets;
        info->totalDrops = retStatisticInfo.total_drops;
        info->sendBitrate = retStatisticInfo.send_bitrate;
        info->audioEncBitrate = retStatisticInfo.audio_enc_bitrate;
        info->videoEncBitrate = retStatisticInfo.video_enc_bitrate;
        info->audioSendBitrate = retStatisticInfo.audio_send_bitrate;
        info->videoSendBitrate = retStatisticInfo.video_send_bitrate;
        info->packageDelayMs = retStatisticInfo.package_delay_ms;

        info->videoEncInPts = retEncoderTsInfo.video_in_pts;
        info->videoEncOutPts = retEncoderTsInfo.video_out_pts;
        info->videoEncOutDts = retEncoderTsInfo.video_out_dts;
        info->audioEncInPts = retEncoderTsInfo.audio_in_pts;
        info->audioEncOutPts = retEncoderTsInfo.audio_out_pts;
        info->audioEncOutDts = retEncoderTsInfo.audio_out_dts;
    }
    return true;
}

bool MediaSDKControllerImplV2::GetActiveFPS(float* fps)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::GetActiveFPS)
    mediasdk::RenderInfo info;
    g_sdk_api.GetRenderInfo(info);
    if (fps)
    {
        *fps = static_cast<float>(info.fps);
    }
    return true;
}

bool MediaSDKControllerImplV2::ReportTeaDataRenderProfiler()
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::ReportTeaDataRenderProfiler)
    g_sdk_api.StartRenderProfiler();
    return true;
}

bool MediaSDKControllerImplV2::ReportTeaDataPerformanceMatrics(const std::vector<PERFORMANCE_MATRICS>& param_list)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::ReportTeaDataPerformanceMatrics)
    nlohmann::json config_json;
    for (const auto& param : param_list)
    {
        nlohmann::json item;
        item["name"] = param.name;
        item["threshold"] = param.threadshold;
        config_json.push_back(item);
    }
    g_sdk_api.StartCollectPerformanceMatrics(config_json.dump().c_str());
    return true;
}

bool MediaSDKControllerImplV2::SetTTNtpMS(uint64_t ntp_ms, uint64_t local_ms)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    g_sdk_api.SetTTNtpMS(static_cast<int64_t>(ntp_ms), static_cast<int64_t>(local_ms));
    return true;
}

bool MediaSDKControllerImplV2::CheckEncoderSession(const std::string& encode_name, uint32_t count, int32_t* result)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    std::string sdkName = MapEncoderName2SDK(encode_name);
    bool        ret = call_helper_.SyncCall<bool>(g_sdk_api.TestEncoderSessionCountSupported, sdkName.c_str(), count);
    if (result)
        *result = ret;
    return true;
}

bool MediaSDKControllerImplV2::GetPresentNotReadyFPS(float* present_not_ready_fps)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::GetPresentNotReadyFPS)
    mediasdk::RenderInfo info;
    g_sdk_api.GetRenderInfo(info);
    if (present_not_ready_fps)
    {
        *present_not_ready_fps = static_cast<float>(info.no_ready_fps);
    }
    return true;
}

bool MediaSDKControllerImplV2::UpdateDynamicConfig(const std::string& json_info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.UpdateABConfig, json_info.c_str());
    return success;
}

bool MediaSDKControllerImplV2::OnKeyboardEvent(uint64_t wparam, uint64_t lparam)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    g_sdk_api.ForwardWindowMessage(click_track_id_, WM_KEYDOWN, wparam, lparam);
    return true;
}

bool MediaSDKControllerImplV2::MockFrozen(int32_t ipc_sleep_timeout_ms, int32_t render_sleep_timeout_ms)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    g_sdk_api.MockRenderHung(render_sleep_timeout_ms);
    return true;
}

bool MediaSDKControllerImplV2::GetVisualFrame(const VisualTextureReq& request, VisualTextureRsp* response)
{
    // TODO, for v3 test
    //{
    //    // v3
    //    int32_t     image_width = 0;
    //    int32_t     image_height = 0;
    //    int32_t     image_row_pitch = 0;
    //    std::string image_data;
    //    int32_t     format = 0;

    //    std::string frame_id = "frame_id_" + request.visual_id;

    //    hook_api_layer_->GetVideoFrameProcessor()->GrabFrame(request.visual_id.c_str(),
    //                                                         frame_id,
    //                                                         v3::ClipResizeOrderEnum::kClipResizeOrder_ResizeFirst,
    //                                                         v3::FitModeEnum::kFitModeEnum_Contain,
    //                                                         request.targetWidth,
    //                                                         request.targetHeight,
    //                                                         request.clip_x,
    //                                                         request.clip_y,
    //                                                         request.clip_z,
    //                                                         request.clip_w
    //                                                         );
    //}

    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	mediasdk::MSClip clip = { request.clip_x, request.clip_y, request.clip_z, request.clip_w };
	auto ret = call_helper_.SyncCall<mediasdk::ResultBoolMSVisualFrame>(g_sdk_api.GetVisualFrame, request.visual_id.c_str(), request.targetWidth, request.targetHeight, clip);
	if (!ret.success)
		return false;
	const auto& visualFrame = ret.frame;
	if (response)
	{
		response->width = visualFrame.width;
		response->height = visualFrame.height;
		response->row_pitch = visualFrame.line_size;
		response->data.resize(response->row_pitch * response->height);
		response->format = MapVideoFormat2Native(visualFrame.format, nullptr);
		memcpy(response->data.data(), visualFrame.buffer, response->row_pitch * response->height);
	}
	return true;
}

v3::AudioFrameProcessor* MediaSDKControllerImplV2::GetAudioFrameProcessor()
{
    return hook_api_layer_->GetAudioFrameProcessor();
}

v3::VideoFrameProcessor* MediaSDKControllerImplV2::GetVideoFrameProcessor()
{
    return hook_api_layer_->GetVideoFrameProcessor();
}

bool MediaSDKControllerImplV2::CreateCanvas(const std::string& canvas_id, const uint32_t video_model_id)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	auto ret = call_helper_.SyncCall<bool>(g_sdk_api.CreateCanvas, canvas_id.c_str(), video_model_id);
	if (!ret)
		LOG(ERROR) << "[MediaSDKControllerImplV2::CreateCanvas] CreateCanvas failed";
	return true;
}

bool MediaSDKControllerImplV2::DestoryCanvas(const std::string& canvas_id)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	auto ret = call_helper_.SyncCall<bool>(g_sdk_api.DestroyCanvas, canvas_id.c_str());
	if (!ret)
		LOG(ERROR) << "[MediaSDKControllerImplV2::DestoryCanvas] DestroyCanvas failed";
	return ret;
}

bool MediaSDKControllerImplV2::GetCurrentCanvas(const uint32_t video_model_id, std::string* canvas_id)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(false, MediaSDKControllerImplV2::GetCurrentCanvas)
	auto ret = call_helper_.SyncCall<mediasdk::MediaSDKString>(g_sdk_api.GetCurrentCanvas, video_model_id);
	*canvas_id = ret.ToString();
	return !ret.empty();
}

bool MediaSDKControllerImplV2::SetCurrentCanvas(const uint32_t video_model_id, const std::string& canvas_id, const std::string& transition_id)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	auto ret = call_helper_.SyncCall<bool>(g_sdk_api.SetCurrentCanvas, video_model_id, canvas_id.c_str(), transition_id.c_str());
	if (!ret)
		LOG(ERROR) << "[MediaSDKControllerImplV2::ShowCanvas] ShowCanvas failed";
	return ret;
}

void SetTransitionParams(mediasdk::CreateTransitionParams& transition_params, const TransitionFilter& transition_filter)
{
    nlohmann::json transition_json;
    transition_json["duration_ms"] = transition_filter.durationMs;
    if (transition_filter.transitionType == TRANSITION_TYPE_SLIDE)
    {
        transition_json["direction"] = transition_filter.slideTransition.direction;
        transition_json["function_type"] = transition_filter.slideTransition.func;
    }
    else if (transition_filter.transitionType == TRANSITION_TYPE_SWIPE)
    {
        transition_json["direction"] = transition_filter.swipeTransition.direction;
        transition_json["swipe_type"] = transition_filter.swipeTransition.swipeType;
        transition_json["function_type"] = transition_filter.swipeTransition.func;
    }
    else if (transition_filter.transitionType == TRANSITION_TYPE_FADE)
    {
        transition_json["function_type"] = transition_filter.fadeTransition.func;
    }
    else if (transition_filter.transitionType == TRANSITION_TYPE_FADE2COLOR)
    {
        transition_json["color"] = transition_filter.fade2ColorTransition.color;
        transition_json["function_type"] = transition_filter.fade2ColorTransition.func;
        transition_json["middle_progress"] = transition_filter.fade2ColorTransition.middleProgress;
    }
    else if (transition_filter.transitionType == TRANSITION_TYPE_CARTOON)
    {
        transition_json["middle_progress"] = transition_filter.cartoonTransition.middleProgress;
        transition_json["file_path"] = transition_filter.cartoonTransition.filePath;
    }
    else if (transition_filter.transitionType == TRANSITION_TYPE_LUMINANCE_WIDE)
    {
        transition_json["function_type"] = transition_filter.luminanceWideTransition.func;
        transition_json["softness"] = transition_filter.luminanceWideTransition.softness;
        transition_json["file_path"] = transition_filter.luminanceWideTransition.filePath;
        transition_json["invert"] = transition_filter.luminanceWideTransition.invert;
    }
    else if (transition_filter.transitionType == TRANSITION_TYPE_MOVE)
    {
        transition_json["middle_progress"] = transition_filter.moveTransition.middleProgress;

        {
            nlohmann::json compared_items_json;
            for (const auto& matchedLayerID : transition_filter.moveTransition.matchedLayerIDs)
            {
                nlohmann::json json_item;
                json_item["source"] = matchedLayerID.first;
                json_item["target"] = matchedLayerID.second;
                compared_items_json.push_back(json_item);
            }
            transition_json["compared_items"] = compared_items_json;
        }

        {
            nlohmann::json move_param_json;
            move_param_json["move_type"] = transition_filter.moveTransition.moveTransitionInfo.moveType;
            move_param_json["move_in_function"] = transition_filter.moveTransition.moveTransitionInfo.moveInFunc;
            move_param_json["move_out_function"] = transition_filter.moveTransition.moveTransitionInfo.moveOutFunc;
            move_param_json["move_to_function"] = transition_filter.moveTransition.moveTransitionInfo.move2Func;
            move_param_json["move_in_from_direction"] = transition_filter.moveTransition.moveTransitionInfo.moveInFromDirection;
            move_param_json["move_out_to_direction"] = transition_filter.moveTransition.moveTransitionInfo.moveOut2Direction;
            {
                nlohmann::json move_in_from_position_json;
                move_in_from_position_json["x"] = transition_filter.moveTransition.moveTransitionInfo.moveInFromPos.X;
                move_in_from_position_json["y"] = transition_filter.moveTransition.moveTransitionInfo.moveInFromPos.Y;
                move_param_json["move_in_from_position"] = move_in_from_position_json;
            }
            {
                nlohmann::json move_out_to_position_json;
                move_out_to_position_json["x"] = transition_filter.moveTransition.moveTransitionInfo.moveOut2Pos.X;
                move_out_to_position_json["y"] = transition_filter.moveTransition.moveTransitionInfo.moveOut2Pos.Y;
                move_param_json["move_out_to_position"] = move_out_to_position_json;
            }
            transition_json["default_move_param"] = move_param_json;
        }
    }
    transition_params.transition_property = transition_json.dump().c_str();
}

bool MediaSDKControllerImplV2::CreateTransition(const std::string& canvas_id, const std::string& transition_id, const TransitionFilter& transition_filter)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::CreateTransitionParams transition_params{};
    bool ok = false;
    transition_params.transition_type = MapTransitionType2SDK(transition_filter.transitionType, &ok);
    if (!ok)
    {
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateTransition] MapTransitionType2SDK failed";
        return false;
    }
    SetTransitionParams(transition_params, transition_filter);

    LOG(INFO) << "[MediaSDKControllerImplV2::CreateTransition] transition_type: " << transition_params.transition_type << ", transition_property: " << transition_params.transition_property.ToString();

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.CreateTransition, transition_id.c_str(), transition_params);
    return success;
}

bool MediaSDKControllerImplV2::DestroyTransition(const std::string& canvas_id, const std::string& transition_id)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = call_helper_.SyncCall<bool>(g_sdk_api.DestroyTransition, transition_id.c_str());
    return success;
}

bool MediaSDKControllerImplV2::SetTransitionProperty(const std::string canvas_id, const std::string& transition_id, const TransitionFilter& transition_filter)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::CreateTransitionParams transition_params{};
    SetTransitionParams(transition_params, transition_filter);

    LOG(INFO) << "[MediaSDKControllerImplV2::CreateTransition] transition_type: " << transition_params.transition_type << ", transition_property: " << transition_params.transition_property.ToString();

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.SetTransitionProperty, transition_id.c_str(), transition_params.transition_property.ToString().c_str());
    return success;
}

bool MediaSDKControllerImplV2::VibeTriggerEffect(const VIBE_TRIGGER_EFFECT& vibeTriggerEffect)
{
    nlohmann::json ambient_json;
    {
        nlohmann::json canvas_item_zorder_json;
        {
            const VIBE_LAYERS_ZORDER& layers_zorder = vibeTriggerEffect.layersZorder;
            canvas_item_zorder_json["action_id"] = layers_zorder.actionID;
            canvas_item_zorder_json["canvas_id"] = vibeTriggerEffect.canvasID;
            canvas_item_zorder_json["canvas_items_z_order"] = layers_zorder.layerIDs;

            ambient_json["canvas_item_z_order"] = canvas_item_zorder_json;
        }

        nlohmann::json canvas_item_transforms = nlohmann::json::array();
        {
            for (int i = 0; i < vibeTriggerEffect.layersTransform.size(); ++i)
            {
                const VIBE_LAYER_TRANSFORM& layer_transform = vibeTriggerEffect.layersTransform[i];
                nlohmann::json              layer_transform_json;
                {
                    layer_transform_json["action_id"] = layer_transform.actionID;
                    layer_transform_json["canvas_item_id"] = layer_transform.layerID;
                    layer_transform_json["duration_ms"] = layer_transform.durationMs;
                    layer_transform_json["progress_function"] = layer_transform.transitionProgressFuncType;

                    nlohmann::json target_transform_json;
                    {
                        const TRANSFORM& target_transform = layer_transform.targetTransform;
                        nlohmann::json   translate_json;
                        {
                            translate_json["x"] = target_transform.translate.X;
                            translate_json["y"] = target_transform.translate.Y;
                            target_transform_json["translate"] = translate_json;
                        }
                        nlohmann::json scale_json;
                        {
                            scale_json["x"] = target_transform.scale.X;
                            scale_json["y"] = target_transform.scale.Y;
                            target_transform_json["scale"] = scale_json;
                        }
                        nlohmann::json crop_json;
                        {
                            crop_json["x"] = target_transform.clipRange.x;
                            crop_json["y"] = target_transform.clipRange.y;
                            crop_json["z"] = target_transform.clipRange.z;
                            crop_json["w"] = target_transform.clipRange.w;
                            target_transform_json["crop"] = crop_json;
                        }
                        nlohmann::json flip_json;
                        {
                            flip_json["h"] = target_transform.hFlip;
                            flip_json["v"] = target_transform.vFlip;
                            target_transform_json["flip"] = flip_json;
                        }
                        target_transform_json["rotate"] = target_transform.angle;
                    }
                    layer_transform_json["target_transform"] = target_transform_json;
                }
                canvas_item_transforms.push_back(layer_transform_json);
            }

            ambient_json["canvas_item_transform"] = canvas_item_transforms;
        }
        
        nlohmann::json canvas_item_visibles = nlohmann::json::array();
        {
            for (int i = 0; i < vibeTriggerEffect.layersVisible.size(); ++i)
            {
                const VIBE_LAYER_VISIBLE& layer_visible = vibeTriggerEffect.layersVisible[i];
                nlohmann::json            layer_visible_json;
                {
                    layer_visible_json["action_id"] = layer_visible.actionID;
                    layer_visible_json["canvas_item_id"] = layer_visible.layerID;
                    layer_visible_json["visible"] = layer_visible.visible;
                    layer_visible_json["duration_ms"] = layer_visible.durationMs;
                    layer_visible_json["progress_function"] = layer_visible.transitionProgressFuncType;
                    canvas_item_visibles.push_back(layer_visible_json);
                }
            }

            ambient_json["canvas_item_visible"] = canvas_item_visibles;
        }
        
        nlohmann::json canvas_item_preprocess_arra;
        {
            const VIBE_PREPROCESS_INFO& preprocess_info = vibeTriggerEffect.preprocessInfo;
            canvas_item_preprocess_json["action_id"] = preprocess_info.actionID;
            for (int i = 0; i < preprocess_info.layersPreprocess.size(); ++i)
            {
                const VIBE_LAYER_PREPROCESS& layer_preprocess = preprocess_info.layersPreprocess[i];
                canvas_item_preprocess_json[layer_preprocess.layerID] = layer_preprocess.needPreprocess;
            }

            ambient_json["canvas_item_preprocess"] = canvas_item_preprocess_json;
        }

        nlohmann::json canvas_item_filter_json;
        nlohmann::json canvas_filter_json;
        {
            for (int i = 0; i < vibeTriggerEffect.filterInfos.size(); ++i)
            {
                const VIBE_FILTER_INFO& filter_info = vibeTriggerEffect.filterInfos[i];
                const FILTER&           filter = filter_info.filter;

                std::string media_id = "";
                Util::NumToString(filter.mediaIDs[0], &media_id);
                std::string filter_id = "";
                Util::NumToString(filter.id, &filter_id);

                if (filter.type == FILTER_VISUAL || filter.type == FILTER_EFFECT)
                {
                    canvas_item_filter_json["action_id"] = filter_info.actionID;
                    canvas_item_filter_json["canvas_item_id"] = media_id;
                    canvas_item_filter_json["filter_id"] = filter_id;
                    if (filter_info.actionType == VIBE_ACTION_TYPE_CREATE)
                    {
                        canvas_item_filter_json["action_type"] = "create";
                        nlohmann::json filter_params_json;
                        if (filter.type == FILTER_VISUAL)
                        {
                            const VISUAL_FILTER& visual_filter = std::get<VISUAL_FILTER>(filter.filter);
                            if (visual_filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                            {
                                canvas_item_filter_json["filter_name"] = "ColorAdjustmentVisualFilter";
                                {
                                    filter_params_json["brightness"] = visual_filter.colorAdjustFilter.brightness;
                                    filter_params_json["contrast"] = visual_filter.colorAdjustFilter.contrast;
                                    filter_params_json["gamma"] = visual_filter.colorAdjustFilter.gamma;
                                    filter_params_json["hue_shift"] = visual_filter.colorAdjustFilter.hueShift;
                                    filter_params_json["opacity"] = visual_filter.colorAdjustFilter.opacity;
                                    filter_params_json["saturation"] = visual_filter.colorAdjustFilter.saturation;
                                    filter_params_json["add_color"] = visual_filter.colorAdjustFilter.addColor;
                                    filter_params_json["mul_color"] = visual_filter.colorAdjustFilter.mulColor;
                                }
                            }
                            canvas_item_filter_json["filter_params"] = filter_params_json;
                        }
                        else if (filter.type == FILTER_EFFECT)
                        {
                            canvas_item_filter_json["filter_name"] = mediasdk::MediaSDKString(mediasdk::effect_visual_filter::kEffectVisualFilterName).ToString().c_str();
                            {
                                std::vector<FILTER> filters{filter};
                                std::vector<std::string> composers;
                                std::vector<std::string> tags{};
                                EFFECT_FILTER            effect_filter{};
                                GetVisualFilterParams(filters, composers, effect_filter, tags);

                                filter_params_json["composers"] = composers;
                                filter_params_json["useEmptyFrameBeforeEffectProcess"] = false;
                                if (!composers.empty() || (!effect_filter.keyPath.key.empty() && !effect_filter.keyPath.val.empty()))
                                {
                                    filter_params_json["useEmptyFrameBeforeEffectProcess"] = true;
                                }
                                canvas_item_filter_json["filter_params"] = filter_params_json.dump().c_str();
                            }
                        }
                    }
                    else if (filter_info.actionType == VIBE_ACTION_TYPE_UPDATE)
                    {
                        if (filter.type == FILTER_EFFECT)
                        {
                            std::vector<FILTER>      filters{filter};
                            std::vector<std::string> composers;
                            std::vector<std::string> tags{};
                            EFFECT_FILTER            effect_filter{};
                            GetVisualFilterParams(filters, composers, effect_filter, tags);

                            canvas_item_filter_json["action_type"] = "set_property";
                            canvas_item_filter_json["property_key"] = "composers";
                            nlohmann::json filter_params_json;
                            {
                                filter_params_json["composers"] = composers;
                                canvas_item_filter_json["property_value"] = filter_params_json.dump().c_str();
                            }
                        }
                    }
                    else if (filter_info.actionType == VIBE_ACTION_TYPE_DESTROY)
                    {
                        if (filter.type == FILTER_VISUAL)
                        {
                            canvas_item_filter_json["action_type"] = "destroy";
                        }
                    }
                    else if (filter_info.actionType == VIBE_ACTION_TYPE_ACTIVE)
                    {
                        canvas_item_filter_json["action_type"] = "set_active";
                        canvas_item_filter_json["active"] = filter.enable.has_value() ? filter.enable.value() : false;
                    }
                }
                else if (filter.type == FILTER_CANVAS)
                {
                    canvas_filter_json["action_id"] = filter_info.actionID;
                    canvas_filter_json["canvas_item_id"] = media_id;
                    canvas_filter_json["filter_id"] = filter_id;
                    if (filter_info.actionType == VIBE_ACTION_TYPE_CREATE)
                    {
                        canvas_filter_json["action_type"] = "create";
                        const CANVAS_FILTER& canvas_filter = std::get<CANVAS_FILTER>(filter.filter);
                        nlohmann::json       filter_params_json;
                        if (canvas_filter.filterType == CANVAS_FILTER_COLOR_ADJUST)
                        {
                            canvas_item_filter_json["filter_name"] = "ColorAdjustmentVisualFilter";
                            {
                                filter_params_json["brightness"] = canvas_filter.colorAdjustFilter.brightness;
                                filter_params_json["contrast"] = canvas_filter.colorAdjustFilter.contrast;
                                filter_params_json["gamma"] = canvas_filter.colorAdjustFilter.gamma;
                                filter_params_json["hue_shift"] = canvas_filter.colorAdjustFilter.hueShift;
                                filter_params_json["opacity"] = canvas_filter.colorAdjustFilter.opacity;
                                filter_params_json["saturation"] = canvas_filter.colorAdjustFilter.saturation;
                                filter_params_json["add_color"] = canvas_filter.colorAdjustFilter.addColor;
                                filter_params_json["mul_color"] = canvas_filter.colorAdjustFilter.mulColor;
                            }
                        }
                        canvas_filter_json["filter_params"] = filter_params_json;
                    }
                    else if (filter_info.actionType == VIBE_ACTION_TYPE_DESTROY)
                    {
                        canvas_filter_json["action_type"] = "destroy";
                    }
                    else if (filter_info.actionType == VIBE_ACTION_TYPE_ACTIVE)
                    {
                        canvas_filter_json["action_type"] = "set_active";
                        canvas_filter_json["active"] = filter.enable.has_value() ? filter.enable.value() : false;
                    }
                }
            }

            ambient_json["canvas_item_filter"] = canvas_item_filter_json;
            ambient_json["canvas_filter"] = canvas_filter_json;
        }
        
        nlohmann::json audio_ambient_json;
        {
            const VIBE_AUDIO_AMBIENT& audio_ambient = vibeTriggerEffect.audioAmbient;
            audio_ambient_json["action_id"] = audio_ambient.actionID;
            if (audio_ambient.actionType == VIBE_ACTION_TYPE_PLAY)
            {
                audio_ambient_json["action_type"] = "start";
            }
            else if (audio_ambient.actionType == VIBE_ACTION_TYPE_STOP)
            {
                audio_ambient_json["action_type"] = "stop";
            }
            else
            {
                audio_ambient_json["action_type"] = "";
            }
            
            audio_ambient_json["audio_input_id"] = audio_ambient.audioID;
            if (audio_ambient.actionType == VIBE_ACTION_TYPE_PLAY)
            {
                audio_ambient_json["left_channel_datas"] = audio_ambient.audioData.bufLeft;
                audio_ambient_json["right_channel_datas"] = audio_ambient.audioData.bufRight;
                audio_ambient_json["sample_rate"] = audio_ambient.audioCapture.samplePerSec;
                audio_ambient_json["channel_num"] = audio_ambient.audioCapture.channels;
                audio_ambient_json["layout"] = audio_ambient.audioCapture.channelLayout;
                audio_ambient_json["format"] = audio_ambient.audioCapture.audioFormat;
            }

            ambient_json["audio_ambient"] = audio_ambient_json;
        }
    }

    bool success = call_helper_.SyncCall<bool>(g_sdk_api.AmbientEffect, ambient_json.dump().c_str());
    if (!success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::VibeTriggerEffect] AmbientEffect failed";
    return success;
}

bool MediaSDKControllerImplV2::CreateLayer(const std::string& layer_id, const std::string& canvas_id, const std::string& source_id, const LAYER_INFO& layer_info)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	LOG(INFO) << "[MediaSDKControllerImplV2::CreateLayer] layer_id: " << layer_id << ", canvas_id: " << canvas_id << ", source_id: " << source_id << ", transform: " << layer_info.transform.toString() << ", visible: " << layer_info.show;
	mediasdk::CreateCanvasItemParams param = GetCanvasItemParam(layer_info.transform, layer_info.show);
	auto ret = call_helper_.SyncCall<bool>(g_sdk_api.CreateCanvasItem, layer_id.c_str(), canvas_id.c_str(), source_id.c_str(), param);
	if (!ret)
	{
		LOG(ERROR) << "[MediaSDKControllerImplV2::CreateLayer] CreateCanvasItem failed";
	}
	return ret;
}

mediasdk::CreateVisualFilterParams MediaSDKControllerImplV2::GetVisualFilterParams(const std::vector<FILTER>& filters, std::vector<std::string>& composers, EFFECT_FILTER& effect_filter, std::vector<std::string>& tags)
{
    mediasdk::CreateVisualFilterParams filter_params{};
    filter_params.filter_name = mediasdk::MediaSDKString(mediasdk::effect_visual_filter::kEffectVisualFilterName);

    nlohmann::json json_root;
    for (const auto& filter_info : filters)
    {
        if (filter_info.type == FILTER_EFFECT)
        {
            effect_filter = std::get<EFFECT_FILTER>(filter_info.filter);
            break;
        }
    }

    if (!effect_filter.keyPath.key.empty())
    {
        json_root["backgroundKey"] = effect_filter.keyPath.key;
    }
    if (!effect_filter.keyPath.val.empty())
    {
        json_root["backgroundPath"] = effect_filter.keyPath.val;
    }

    for (auto& composer : effect_filter.composers)
    {
        std::string composer_str = ComposerToStr(composer);
        if (!composer_str.empty())
        {
            composers.push_back(composer_str);
            tags.push_back(composer.composerTag);
        }
    }

    if (effect_filter.brightConfig.enable)
    {
        EFFECT_COMPOSER bright_composer{};
        bright_composer.effectPath = effect_filter.brightConfig.assetPath;
        bright_composer.keyVal = effect_filter.brightConfig.keyVal;
        composers.push_back(ComposerToStr(bright_composer));
    }
    json_root["composers"] = composers;
    json_root["tags"] = tags;
    json_root["useEmptyFrameBeforeEffectProcess"] = false;
    if (!composers.empty() || (!effect_filter.keyPath.key.empty() && !effect_filter.keyPath.val.empty()))
    {
        json_root["useEmptyFrameBeforeEffectProcess"] = true;
    }
    filter_params.json_params = mediasdk::MediaSDKString(json_root.dump().c_str());
    return filter_params;
}

bool MediaSDKControllerImplV2::CreateLayerWithFilter(const std::string& layer_id, const std::string& canvas_id, const std::string& source_id, const LAYER_INFO& layer_info, bool need_preprocess)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
	LOG(INFO) << "[MediaSDKControllerImplV2::CreateLayerWithFilter] layer_id: " << layer_id << ", canvas_id: " << canvas_id << ", source_id: " << source_id << ", transform: " << layer_info.transform.toString() << ", visible: " << layer_info.show << ", need_process: " << need_preprocess;
	mediasdk::CreateCanvasItemParams canvas_item_param = GetCanvasItemParam(layer_info.transform, layer_info.show);
	
    std::vector<std::string> composers{};
    std::vector<std::string> tags{};
    EFFECT_FILTER effect_filter{};
    const auto& filter_params = GetVisualFilterParams(layer_info.filters, composers, effect_filter, tags);
	std::string filter_id = layer_id + "_filter";
	auto success = call_helper_.SyncCall<bool>(g_sdk_api.CreateCanvasItemWithFilter, layer_id.c_str(), canvas_id.c_str(), source_id.c_str(), canvas_item_param, filter_id.c_str(), filter_params);
	if (!success)
	{
		LOG(ERROR) << "[MediaSDKControllerImplV2::CreateLayerWithFilter] CreateCanvasItemWithFilter failed";
		return false;
	}

    if (composers.empty() && (effect_filter.keyPath.key.empty() || effect_filter.keyPath.val.empty()))
    {
        EffectEnable(layer_id, false);
    }

	success = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemPreprocess, layer_id.c_str(), need_preprocess);
	if (!success)
	{
		LOG(ERROR) << "[MediaSDKControllerImplV2::CreateLayerWithFilter] SetCanvasItemPreprocess error!";
		return false;
	}
	return success;
}

bool MediaSDKControllerImplV2::CreateEffectVisualFilter(const std::string& layer_id, const LAYER_INFO& layer_info)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    LOG(INFO) << "[MediaSDKControllerImplV2::CreateLayerWithFilter] layer_id: " << layer_id;
    std::vector<std::string> composers{};
    std::vector<std::string> tags{};
    EFFECT_FILTER            effect_filter{};
    const auto&              filter_params = GetVisualFilterParams(layer_info.filters, composers, effect_filter, tags);
    std::string              filter_id = layer_id + "_filter";
    bool                     success = call_helper_.SyncCall<bool>(g_sdk_api.CreateVisualFilter, filter_id.c_str(), filter_params.filter_name.ToString().c_str(), layer_id.c_str(), filter_params.json_params.ToString().c_str());
    if (!success)
        LOG(ERROR) << "[MediaSDKControllerImplV2::CreateEffectVisualFilter] CreateVisualFilter failed, layer_id: " << layer_id;
    return success;
}

std::string MediaSDKControllerImplV2::ComposerToStr(const EFFECT_COMPOSER& composer)
{
	std::string composer_str = "";
	if (!composer.effectPath.empty())
	{
		composer_str += composer.effectPath;
	}

	if (!composer.keyVal.key.empty())
	{
		composer_str += ";";
		composer_str += composer.keyVal.key;
	}

	if (!composer.keyVal.val.empty())
	{
		composer_str += ";";
		composer_str += composer.keyVal.val;
	}

	return composer_str;
}

bool MediaSDKControllerImplV2::DestroyLayer(const std::string& canvas_item_id)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    auto ret = call_helper_.SyncCall<bool>(g_sdk_api.DestroyCanvasItem, canvas_item_id.c_str());
	if (!ret)
	{
		LOG(WARNING) << "[MediaSDKControllerImplV2::DestroyCanvasItem] DestroyCanvasItem failed";
        return false;
	}
	return ret;
}

bool MediaSDKControllerImplV2::UpdateLayerBindSourceWithTransform(const std::string& layer_id, const std::string& source_id, const TRANSFORM& transform)
{
	CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    mediasdk::MSTransform info = MapTransform2SDK(transform);
    auto ret = call_helper_.SyncCall<bool>(g_sdk_api.CanvasItemChangeVisual, layer_id.c_str(), source_id.c_str(), &info);
	if (!ret)
	{
		LOG(ERROR) << "[MediaSDKControllerImplV2::UpdateLayerBindSourceWithTransform] CanvasItemChangeVisual failed";
		return false;
	}

	return ret;
}

bool MediaSDKControllerImplV2::GetCurrentCanvasItemOnVideoModel(const std::string& canvas_id)
{
	// TODO: @xuwanhui
	return false;
}

bool MediaSDKControllerImplV2::GetVisualFromCanvasItem(const std::string& canvas_id)
{
	// TODO: @xuwanhui
	return false;
}

mediasdk::CreateCanvasItemParams MediaSDKControllerImplV2::GetCanvasItemParam(TRANSFORM transform, bool visible)
{
	mediasdk::CreateCanvasItemParams param{};
	param.is_visible = visible;
	param.transform.translate.x = transform.translate.X;
	param.transform.translate.y = transform.translate.Y;
	param.transform.scale.x = transform.scale.X;
	param.transform.scale.y = transform.scale.Y;
	param.transform.flip_h = transform.hFlip;
	param.transform.flip_v = transform.vFlip;
	param.transform.angle = transform.angle;
	param.transform.clip.x = transform.clipRange.x;
	param.transform.clip.y = transform.clipRange.y;
	param.transform.clip.z = transform.clipRange.z;
	param.transform.clip.w = transform.clipRange.w;
	return param;
}

static void MapVideoQualityManagerGearResolution(const VideoQualityManagerGearResolution& in, nlohmann::json* out)
{
    nlohmann::json wide_screen = nlohmann::json::array();
    for (const auto val : in.wide_screen)
    {
        wide_screen.push_back(val);
    }

    nlohmann::json standard_screen = nlohmann::json::array();
    for (const auto val : in.standard_screen)
    {
        standard_screen.push_back(val);
    }

    (*out)["wide_screen"] = wide_screen;
    (*out)["standard_screen"] = standard_screen;
}

static void MapVideoQualityManagerGear(const VideoQualityManagerGear& in, nlohmann::json* out)
{
    nlohmann::json resolution;
    MapVideoQualityManagerGearResolution(in.resolution, &resolution);

    (*out)["id"] = in.id;
    (*out)["name"] = in.name;
    (*out)["resolution"] = resolution;
    (*out)["fps"] = in.fps;
}

static void MapVideoQualityManagerBwGearIdEntry(const VideoQualityManagerBwGearIdEntry& in, nlohmann::json* out)
{
    (*out)["min"] = in.min;
    (*out)["max"] = in.max;
    (*out)["gear_id"] = in.gear_id;
}

static void MapVideoQualityManagerVideoBitrate(const VideoQualityManagerVideoBitrate& in, nlohmann::json* out)
{
    (*out)["min"] = in.min;
    (*out)["target"] = in.target;
    (*out)["max"] = in.max;
}

static void MapVideoQualityManagerBitrate(const VideoQualityManagerBitrate& in, nlohmann::json* out)
{
    nlohmann::json video_wide_screen;
    for (auto it : in.video_wide_screen)
    {
        nlohmann::json val;
        MapVideoQualityManagerVideoBitrate(it.second, &val);
        video_wide_screen[it.first] = val;
    }

    nlohmann::json video_standard_screen;
    for (auto it : in.video_standard_screen)
    {
        nlohmann::json val;
        MapVideoQualityManagerVideoBitrate(it.second, &val);
        video_standard_screen[it.first] = val;
    }

    (*out)["audio"] = in.audio;
    (*out)["gear_id"] = in.gear_id;
    (*out)["video_wide_screen"] = video_wide_screen;
    (*out)["video_standard_screen"] = video_standard_screen;
}

static void MapVideoQualityManagerTopicConfig(const VideoQualityManagerTopicConfig& in, nlohmann::json* out)
{
    nlohmann::json bw_table = nlohmann::json::array();
    for (const auto& info : in.bw_table)
    {
        nlohmann::json val;
        MapVideoQualityManagerBwGearIdEntry(info, &val);
        bw_table.push_back(val);
    }

    nlohmann::json device_level_table;
    for (const auto it : in.device_level_table)
    {
        device_level_table[it.first] = it.second;
    }

    nlohmann::json bitrate_table = nlohmann::json::array();
    for (auto const& info : in.bitrate_table)
    {
        nlohmann::json val;
        MapVideoQualityManagerBitrate(info, &val);
        bitrate_table.push_back(val);
    }

    nlohmann::json topic_id_list = nlohmann::json::array();
    for (auto val : in.topic_id_list)
    {
        topic_id_list.push_back(val);
    }

    (*out)["bw_table"] = bw_table;
    (*out)["device_level_table"] = device_level_table;
    (*out)["quality_perf_level_step"] = in.quality_perf_level_step;
    (*out)["enable_camera_enc_linkage"] = in.enable_camera_enc_linkage;
    (*out)["bitrate_table"] = bitrate_table;
    (*out)["topic_id_list"] = topic_id_list;
    (*out)["topic_type"] = in.topic_type;
}

static void MapVideoQualityManagerStrategyConfig(const VideoQualityManagerStrategyConfig& in, nlohmann::json* out)
{
    nlohmann::json gear_shift_rule;
    for (auto it : in.gear_shift_rule)
    {
        nlohmann::json val;
        val.push_back(it.first);
        val.push_back(it.second);
        gear_shift_rule.push_back(val);
    }

    (*out)["priority"] = in.priority;
    (*out)["bitrate_table_index"] = in.bitrate_table_index;
    (*out)["default_level"] = in.default_level;
    (*out)["gear_shift_rule"] = gear_shift_rule;
    (*out)["gear_shift_type"] = in.gear_shift_type;
    (*out)["name"] = in.name;
}

static void MapVideoQualityManagerBwReserveFactorEntry(const VideoQualityManagerBwReserveFactorEntry& in, nlohmann::json* out)
{
    (*out)["min"] = in.min;
    (*out)["max"] = in.max;
    (*out)["reservation"] = in.reservation;
}

static void MapVideoQualityManagerDualCanvasConfig(const VideoQualityManagerDualCanvasConfig& in, nlohmann::json* out)
{
    nlohmann::json bw_probe_reservation = nlohmann::json::array();
    for (const auto& info : in.bw_probe_reservation)
    {
        nlohmann::json val;
        MapVideoQualityManagerBwReserveFactorEntry(info, &val);
        bw_probe_reservation.push_back(val);
    }

    (*out)["bw_probe_reservation"] = bw_probe_reservation;
    (*out)["bitrate_alloc_ratio"] = in.bitrate_alloc_ratio;
}

static void MapVideoQualityManagerCameraSuggestedConfig(const VideoQualityManagerCameraSuggestedConfig& in, nlohmann::json* out)
{
    nlohmann::json resolution = nlohmann::json::array();
    for (const auto& val : in.resolution)
    {
        resolution.push_back(val);
    }

    nlohmann::json fps = nlohmann::json::array();
    for (const auto& val : in.fps)
    {
        fps.push_back(val);
    }

    (*out)["resolution"] = resolution;
    (*out)["fps"] = fps;
}

static void MapVideoQualityManagerCameraRecommendConfig(const VideoQualityManagerCameraRecommendConfig& in, nlohmann::json* out)
{
    nlohmann::json device_level_camera_recommend_config;
    for (const auto it : in.device_level_camera_recommend_config)
    {
        nlohmann::json val;
        MapVideoQualityManagerCameraSuggestedConfig(it.second, &val);
        device_level_camera_recommend_config[it.first] = val;
    }

    (*out)["device_level_camera_recommend_config"] = device_level_camera_recommend_config;
    (*out)["raise_threshold_for_fps"] = in.raise_threshold_for_fps;
}

static void MapVideoQualityManagerRect(const VideoQualityManagerRect& in, nlohmann::json* out)
{
    (*out)["width"] = in.width;
    (*out)["height"] = in.height;
}

static void MapVideoQualityManagerCameraDefaultRecommend(const VideoQualityManagerCameraDefaultRecommend& in, nlohmann::json* out)
{
    nlohmann::json resolution;
    MapVideoQualityManagerRect(in.resolution, &resolution);

    (*out)["resolution"] = resolution;
    (*out)["fps"] = in.fps;
}

static void MapVideoQualityManagerRecommendStrategy(const VideoQualityManagerRecommendStrategy& in, nlohmann::json* out)
{
    if (!out)
        return;
    nlohmann::json topic_config = nlohmann::json::array();
    for (const auto& info : in.topic_config)
    {
        nlohmann::json val;
        MapVideoQualityManagerTopicConfig(info, &val);
        topic_config.push_back(val);
    }

    nlohmann::json gear_table_priority = nlohmann::json::array();
    for (const auto& info : in.gear_table_priority)
    {
        nlohmann::json val;
        MapVideoQualityManagerStrategyConfig(info, &val);
        gear_table_priority.push_back(val);
    }

    nlohmann::json dual_canvas_config;
    MapVideoQualityManagerDualCanvasConfig(in.dual_canvas_config, &dual_canvas_config);

    nlohmann::json camera_recommend_config;
    MapVideoQualityManagerCameraRecommendConfig(in.camera_recommend_config, &camera_recommend_config);

    nlohmann::json camera_default_recommend;
    if (in.camera_default_recommend.has_value())
    {
        MapVideoQualityManagerCameraDefaultRecommend(in.camera_default_recommend.value(), &camera_default_recommend);
    }

    (*out)["topic_config"] = topic_config;
    (*out)["gear_table_priority"] = gear_table_priority;
    (*out)["dual_canvas_config"] = dual_canvas_config;
    (*out)["camera_recommend_config"] = camera_recommend_config;
    (*out)["default_bitrate_table_topic"] = in.default_bitrate_table_topic;

    if (!camera_default_recommend.empty())
    {
        (*out)["camera_default_recommend"] = camera_default_recommend;
    }
}

static void MapVideoQualityManagerKVString(const std::map<std::string, std::string>& in, nlohmann::json* out)
{
    for (const auto it : in)
    {
        (*out)[it.first] = it.second;
    }
}

static void MapVideoQualityManagerInitializeParam(const VideoQualityManagerInitializeParam& in, nlohmann::json* out)
{
    if (!out)
        return;

    nlohmann::json gear_table = nlohmann::json::array();
    for (const auto& info : in.gear_table)
    {
        nlohmann::json val;
        MapVideoQualityManagerGear(info, &val);
        gear_table.push_back(val);
    }

    nlohmann::json recommend_strategy;
    MapVideoQualityManagerRecommendStrategy(in.recommend_strategy, &recommend_strategy);

    nlohmann::json current_topic_device_level;
    MapVideoQualityManagerKVString(in.current_topic_device_level, &current_topic_device_level);

    (*out)["gear_table"] = gear_table;
    (*out)["recommend_strategy"] = recommend_strategy;
    (*out)["current_topic_device_level"] = current_topic_device_level;
    (*out)["use_old_recommend_way"] = in.use_old_recommend_way;
}

static void MapVideoQualityManagerGearResolution(const nlohmann::json& in, VideoQualityManagerGearResolution* out)
{
    auto wide_screen = in["wide_screen"];
    if (!wide_screen.empty())
    {
        out->wide_screen = wide_screen.get<std::vector<int32_t>>();
    }

    auto standard_screen = in["standard_screen"];
    if (!standard_screen.empty())
    {
        out->standard_screen = standard_screen.get<std::vector<int32_t>>();
    }
}

static void MapVideoQualityManagerGearOut(const nlohmann::json& in, VideoQualityManagerGearOut* out)
{
    out->id = in["id"].get<int32_t>();
    out->name = in["name"].get<std::string>();
    MapVideoQualityManagerGearResolution(in["resolution"], &out->resolution);
    out->fps = in["fps"].get<int32_t>();
}

static void MapVideoQualityManagerVideoBitrate(const nlohmann::json& in, VideoQualityManagerVideoBitrate* out)
{
    out->min = in["min"].get<int32_t>();
    out->max = in["max"].get<int32_t>();
    out->target = in["target"].get<int32_t>();
}

static void MapVideoQualityManagerBitrateOut(const nlohmann::json& in, VideoQualityManagerBitrateOut* out)
{
    out->audio = in["audio"].get<int32_t>();
    out->gear_id = in["gear_id"].get<int32_t>();

    auto video_wide_screen = in["video_wide_screen"];
    if (!video_wide_screen.empty())
    {
        auto obj = video_wide_screen.get<nlohmann::json::object_t>();

        for (auto it : obj)
        {
            VideoQualityManagerVideoBitrate val;
            MapVideoQualityManagerVideoBitrate(it.second, &val);
            out->video_wide_screen[it.first] = val;
        }
    }

    auto video_standard_screen = in["video_standard_screen"];
    if (!video_standard_screen.empty())
    {
        auto obj = video_standard_screen.get<nlohmann::json::object_t>();

        for (auto it : obj)
        {
            VideoQualityManagerVideoBitrate val;
            MapVideoQualityManagerVideoBitrate(it.second, &val);
            out->video_standard_screen[it.first] = val;
        }
    }

    out->dual_canvas_video_bitrate_alloc_ratio = in["dual_canvas_video_bitrate_alloc_ratio"];
}

static void MapVideoQualityManagerGoLiveParamsOut(const nlohmann::json& in, VideoQualityManagerGoLiveParamsOut* out)
{
    MapVideoQualityManagerGearOut(in["gear"], &out->gear);
    MapVideoQualityManagerBitrateOut(in["bitrate"], &out->bitrate);
    out->decision_process_info = in["decision_process_info"];
}

static void MapVideoQualityManagerCameraParams(const VideoQualityManagerCameraParams& in, nlohmann::json* out)
{
    (*out)["width"] = in.width;
    (*out)["height"] = in.height;
    (*out)["fps"] = in.fps;
    (*out)["max_fps"] = in.max_fps;
    (*out)["min_fps"] = in.min_fps;
    (*out)["format"] = (int32_t)in.format;
}

static void MapVideoQualityManagerGoLiveParams(const VideoQualityManagerGoLiveParams& in, nlohmann::json* out)
{
    (*out)["width"] = in.width;
    (*out)["height"] = in.height;
    (*out)["fps"] = in.fps;
}

static void MapVideoQualityManagerCameraStrategy(const VideoQualityManagerCameraStrategy& in, nlohmann::json* out)
{
    nlohmann::json format_list = nlohmann::json::array();
    for (auto val : in.format_list)
    {
        format_list.push_back((int32_t)val);
    }

    (*out)["format_list"] = format_list;
    (*out)["width"] = in.width;
    (*out)["height"] = in.height;
    (*out)["fps"] = in.fps;
    (*out)["min_fps"] = in.min_fps;
    (*out)["max_fps"] = in.max_fps;
}

static void MapVideoQualityManagerQueryCameraRecommendedParamsRequest(const VideoQualityManagerQueryCameraRecommendedParamsRequest& in, nlohmann::json* out)
{
    nlohmann::json capture_params_list = nlohmann::json::array();
    for (const auto& info : in.capture_params_list)
    {
        nlohmann::json val;
        MapVideoQualityManagerCameraParams(info, &val);
        capture_params_list.push_back(val);
    }

    nlohmann::json current_enc_params;
    if (in.current_enc_params.has_value())
    {
        MapVideoQualityManagerGoLiveParams(in.current_enc_params.value(), &current_enc_params);
    }

    nlohmann::json current_camera_params;
    if (in.current_camera_params.has_value())
    {
        MapVideoQualityManagerCameraParams(in.current_camera_params.value(), &current_camera_params);
    }

    nlohmann::json strategy;
    if (in.strategy.has_value())
    {
        MapVideoQualityManagerCameraStrategy(in.strategy.value(), &strategy);
    }

    (*out)["camera_name"] = in.camera_name;
    if (in.current_device_level.has_value())
        (*out)["current_device_level"] = in.current_device_level.value();

    (*out)["capture_params_list"] = capture_params_list;

    if (in.current_enc_params.has_value())
        (*out)["current_enc_params"] = current_enc_params;
    if (in.current_camera_params.has_value())
        (*out)["current_camera_params"] = current_camera_params;
    if (in.topicId.has_value())
        (*out)["topicId"] = in.topicId.value();
    if (in.strategy.has_value())
        (*out)["strategy"] = strategy;
}

static void MapVideoQualityManagerCameraParamsOut(const nlohmann::json& in, VideoQualityManagerCameraParamsOut* out)
{
    out->width = in["width"].get<int32_t>();
    out->height = in["height"].get<int32_t>();
    out->fps = in["fps"].get<int32_t>();
    out->max_fps = in["max_fps"].get<int32_t>();
    out->min_fps = in["min_fps"].get<int32_t>();
    out->format = (VIDEO_PIXEL_FORMAT)in["format"].get<int32_t>();

    auto optional_fps = in["optional_fps"];
    if (!optional_fps.empty())
    {
        for (size_t i = 0; i < optional_fps.size(); ++i)
        {
            out->optional_fps.push_back(optional_fps[i].get<int32_t>());
        }
    }
}

static void MapVideoQualityManagerQueryCameraRecommendedParamsResponse(const nlohmann::json& in, VideoQualityManagerQueryCameraRecommendedParamsResponse* out)
{
    MapVideoQualityManagerCameraParamsOut(in["recommend_params"], &out->recommend_params);
    out->resolution_prompt = in["resolution_prompt"].get<std::string>();
    out->fps_prompt = in["fps_prompt"].get<std::string>();

    auto optional_params = in["optional_params"];
    if (!optional_params.empty())
    {
        for (size_t i = 0; i < optional_params.size(); ++i)
        {
            VideoQualityManagerCameraParamsOut val;
            MapVideoQualityManagerCameraParamsOut(optional_params[i], &val);
            out->optional_params.push_back(val);
        }
    }
}

static void MapVideoQualityManagerQueryGoLiveRecommendedParamsRequest(const VideoQualityManagerQueryGoLiveRecommendedParamsRequest& in, nlohmann::json* out)
{
    if (in.bw_probe_bps.has_value())
        (*out)["bw_probe_bps"] = in.bw_probe_bps.value();

    if (!in.current_topic_device_level.empty())
    {
        nlohmann::json current_topic_device_level;
        MapVideoQualityManagerKVString(in.current_topic_device_level, &current_topic_device_level);
        (*out)["current_topic_device_level"] = current_topic_device_level;
    }

    if (in.camera_max_params.has_value())
    {
        nlohmann::json camera_max_params;
        MapVideoQualityManagerCameraParams(in.camera_max_params.value(), &camera_max_params);
        (*out)["camera_max_params"] = camera_max_params;
    }
}

static void MapVideoQualityManagerTopicGoLiveParams(const nlohmann::json& in, VideoQualityManagerTopicGoLiveParams* out)
{
    MapVideoQualityManagerGoLiveParamsOut(in["quality_priority"], &out->quality_priority);
    MapVideoQualityManagerGoLiveParamsOut(in["performance_priority"], &out->performance_priority);
}

static void MapVideoQualityManagerRecommendedGoLiveParams(const nlohmann::json& in, VideoQualityManagerRecommendedGoLiveParams* out)
{
    out->topic_type = in["topic_type"];
    auto topic_id_list = in["topic_id_list"];
    for (size_t i = 0; i < topic_id_list.size(); ++i)
    {
        auto val = topic_id_list[i].get<int32_t>();
        out->topic_id_list.push_back(val);
    }

    MapVideoQualityManagerTopicGoLiveParams(in["topic_go_live_params"], &out->topic_go_live_params);
}

static void MapVideoQualityManagerQueryGoLiveRecommendedParamsResponse(const nlohmann::json& in, VideoQualityManagerQueryGoLiveRecommendedParamsResponse* out)
{
    auto go_live_recommend_params = in["go_live_recommend_params"];
    for (size_t i = 0; i < go_live_recommend_params.size(); ++i)
    {
        VideoQualityManagerRecommendedGoLiveParams val;
        MapVideoQualityManagerRecommendedGoLiveParams(go_live_recommend_params[i], &val);
        out->go_live_recommend_params.push_back(val);
    }
}

static void MapVideoQualityManagerQueryCameraBestParamsForTargetRequest(const VideoQualityManagerQueryCameraBestParamsForTargetRequest& in, nlohmann::json* out)
{
    nlohmann::json camera_strategy;
    MapVideoQualityManagerCameraStrategy(in.camera_strategy, &camera_strategy);

    nlohmann::json capture_params_list = nlohmann::json::array();
    for (const auto& info : in.capture_params_list)
    {
        nlohmann::json val;
        MapVideoQualityManagerCameraParams(info, &val);
        capture_params_list.push_back(val);
    }

    (*out)["camera_strategy"] = camera_strategy;
    (*out)["capture_params_list"] = capture_params_list;
    (*out)["current_device_level"] = in.current_device_level;
}

static void MapVideoQualityManagerQueryCameraBestParamsForTargetResponse(const nlohmann::json& in, VideoQualityManagerQueryCameraBestParamsForTargetResponse* out)
{
    auto camera_best_params = in["camera_best_params"];
    MapVideoQualityManagerCameraParamsOut(camera_best_params, &out->camera_best_params);
}

static void MapVideoQualityManagerQueryManuallySelectedResultRequest(const VideoQualityManagerQueryManuallySelectedResultRequest& in, nlohmann::json* out)
{
    (*out)["gear_id"] = in.gear_id;
    if (in.topic_id.has_value())
        (*out)["topic_id"] = in.topic_id.value();
}

static void MapVideoQualityManagerQueryManuallySelectedResultResponse(const nlohmann::json& in, VideoQualityManagerQueryManuallySelectedResultResponse* out)
{
    auto go_live_params = in["go_live_params"];
    MapVideoQualityManagerGoLiveParamsOut(go_live_params, &out->go_live_params);
}

bool MediaSDKControllerImplV2::VideoQualityManagerInitialize(const VideoQualityManagerInitializeParam& param, VideoQualityManagerGoLiveParamsOut* default_go_live_params)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    nlohmann::json in_json;
    MapVideoQualityManagerInitializeParam(param, &in_json);
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolString>(g_sdk_api.InitVQM, in_json.dump().c_str());
    if (!ret.success)
        return false;

    try
    {
        auto rsp_str = ret.value.ToString();
        auto out_json = nlohmann::json::parse(rsp_str);

        if (!out_json.empty())
        {
            if (default_go_live_params)
                MapVideoQualityManagerGoLiveParamsOut(out_json["default_go_live_params"], default_go_live_params);
            success = true;
        }
    }
    catch (...)
    {
        success = false;
    }

    return success;
}

bool MediaSDKControllerImplV2::VideoQualityManagerQueryCameraRecommendedParams(const VideoQualityManagerQueryCameraRecommendedParamsRequest& request, VideoQualityManagerQueryCameraRecommendedParamsResponse* response)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    nlohmann::json in_json;
    MapVideoQualityManagerQueryCameraRecommendedParamsRequest(request, &in_json);

    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolString>(g_sdk_api.QueryCameraRecommendedParams, in_json.dump().c_str());
    if (!ret.success)
        return false;

    try
    {
        auto rsp_str = ret.value.ToString();
        auto out_json = nlohmann::json::parse(rsp_str);
        if (!out_json.empty())
        {
            if (response)
                MapVideoQualityManagerQueryCameraRecommendedParamsResponse(out_json, response);
            success = true;
        }
    }
    catch (...)
    {
        success = false;
    }

    return success;
}

bool MediaSDKControllerImplV2::VideoQualityManagerQueryGoLiveRecommendedParams(const VideoQualityManagerQueryGoLiveRecommendedParamsRequest& request, VideoQualityManagerQueryGoLiveRecommendedParamsResponse* response)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    nlohmann::json in_json;
    MapVideoQualityManagerQueryGoLiveRecommendedParamsRequest(request, &in_json);
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolString>(g_sdk_api.QueryGoLiveRecommendedParams, in_json.dump().c_str());
    if (!ret.success)
        return false;

    try
    {
        auto rsp_str = ret.value.ToString();
        auto out_json = nlohmann::json::parse(rsp_str);
        if (!out_json.empty())
        {
            if (response)
                MapVideoQualityManagerQueryGoLiveRecommendedParamsResponse(out_json, response);
            success = true;
        }
    }
    catch (...)
    {
        success = false;
    }

    return success;
}

bool MediaSDKControllerImplV2::VideoQualityManagerQueryManuallySelectedResult(const VideoQualityManagerQueryManuallySelectedResultRequest& request, VideoQualityManagerQueryManuallySelectedResultResponse* response)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;
    nlohmann::json in_json;
    MapVideoQualityManagerQueryManuallySelectedResultRequest(request, &in_json);

    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolString>(g_sdk_api.QueryGoLiveManuallySelectedParams, in_json.dump().c_str());
    if (!ret.success)
        return false;

    try
    {
        auto rsp_str = ret.value.ToString();
        auto out_json = nlohmann::json::parse(rsp_str);
        if (!out_json.empty())
        {
            if (response)
                MapVideoQualityManagerQueryManuallySelectedResultResponse(out_json, response);
            success = true;
        }
    }
    catch (...)
    {
        success = false;
    }

    return success;
}

bool MediaSDKControllerImplV2::VideoQualityManagerQueryCameraBestParamsForTarget(const VideoQualityManagerQueryCameraBestParamsForTargetRequest& request, VideoQualityManagerQueryCameraBestParamsForTargetResponse* response)
{
    CHECK_INITIALIZED_AND_PRINT_LOG(true, 0)
    bool success = false;

    nlohmann::json in_json;
    MapVideoQualityManagerQueryCameraBestParamsForTargetRequest(request, &in_json);
    auto ret = call_helper_.SyncCall<mediasdk::ResultBoolString>(g_sdk_api.QueryCameraBestParamsForTarget, in_json.dump().c_str());

    if (!ret.success)
        return false;

    try
    {
        auto rsp_str = ret.value.ToString();
        auto out_json = nlohmann::json::parse(rsp_str);
        if (!out_json.empty())
        {
            if (response)
                MapVideoQualityManagerQueryCameraBestParamsForTargetResponse(out_json, response);
            success = true;
        }
    }
    catch (...)
    {
        success = false;
    }

    return success;
}

bool MediaSDKControllerImplV2::ReconfigVideoOutput(const VideoOutputParamsRequest& request, VideoOutputParamsResponse* response)
{
	mediasdk::VideoOutputParams params;
	params.stream_id = request.stream_id;
	params.fps = request.fps;
	params.width = request.width;
	params.height = request.height;
	params.abr_strategy = request.abr_strategy;
	int32_t ret = call_helper_.SyncCall<int32_t>(g_sdk_api.ReconfigVideoOutput, params, request.reason.c_str());
	if (response)
		response->result = ret;
	return true;
}

bool MediaSDKControllerImplV2::FallbackVideoEncoder(const FallbackVideoEncoderParamsRequest& request)
{
	nlohmann::json video_config;
	if (!request.codec_param_json.empty())
	{
		try
		{
			video_config = nlohmann::json::parse(request.codec_param_json);
		}
		catch (const std::exception& e)
		{
			;
		}
	}
	video_config["id"] = request.codec_id;
	video_config["name"] = MapEncoderName2SDK(request.codec_name);
	video_config["bitrate"] = request.bitrate;
	bool ret = call_helper_.SyncCall<bool>(g_sdk_api.FallbackVideoEncoder, request.codec_id, video_config.dump());
	return ret;
}

bool MediaSDKControllerImplV2::StartAdaptiveGearStrategyReport(const std::string& stream_id, const std::string& abr_config)
{
    g_sdk_api.StartAdaptiveGearStrategyReport(mediasdk::MediaSDKString(stream_id), mediasdk::MediaSDKString(abr_config));
    return true;
}

bool MediaSDKControllerImplV2::SetPreprocessDefaultSize(const std::string& visual_id, int cx, int cy)
{
	mediasdk::MSSize size = { cx, cy };
	bool ret = call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemPreprocessDefaultSize, visual_id.c_str(), size);
	return ret;
}

bool MediaSDKControllerImplV2::RemovePreprocessDefaultSize(const std::string& visual_id)
{
	bool ret = call_helper_.SyncCall<bool>(g_sdk_api.RemoveCanvasItemPreprocessDefaultSize, visual_id.c_str());
	return ret;
}

void MediaSDKControllerImplV2::StartConnectAudioCallback()
{
    StopConnectAudioCallback();

    audio_output_buffer_connect_thread_stop_ = false;

    audio_output_buffer_connect_thread_ = 
        std::thread(std::bind(&MediaSDKControllerImplV2::ConnectAudioCallback, this));
}
void MediaSDKControllerImplV2::StopConnectAudioCallback()
{
    audio_output_buffer_connect_thread_stop_ = true;

    if (audio_output_buffer_connect_thread_.joinable())
        audio_output_buffer_connect_thread_.join();

    audio_output_buffer_connect_thread_ = {};

    audio_output_buffer_->Close();
}
void MediaSDKControllerImplV2::ConnectAudioCallback()
{
    bool init_audio_output_buffer_ok = false;

    const char* output_buffer_name = "191EEA47-C864-4B54-B886-59613261483C";

    LOG(INFO) << "StartConnectAudioCallback";

    while (!audio_output_buffer_connect_thread_stop_)
    {

        size_t elem_size = 10240;
        size_t capacity = 240;

        init_audio_output_buffer_ok =
            audio_output_buffer_->Create(output_buffer_name, elem_size, capacity);
        if (init_audio_output_buffer_ok)
            break;

        init_audio_output_buffer_ok =
            audio_output_buffer_->Open(output_buffer_name, &elem_size, &capacity);
        if (init_audio_output_buffer_ok)
            break;

        std::this_thread::sleep_for(std::chrono::milliseconds(200));

    }

    if (init_audio_output_buffer_ok)
    {
        audio_output_buffer_->Clear();
    }

    LOG(INFO) << StringPrintf("EndConnectAudioCallback, success=%d", (int32_t)init_audio_output_buffer_ok);
 
}

void MediaSDKControllerImplV2::OnMonoAudioPCMS16DataCallback(
    const std::string& audio_input_id,
    v3::AudioFrameProcessor::AudioInputFrameTypeEnum frame_type,
    int64_t                                          timestamp_ns,
    int16_t* pcm_s16_data,
    int32_t                                          nb_sample_count)
{

    audio_output_buffer_->Push([&](char* dst, size_t max_len) -> bool {

        size_t data_len = nb_sample_count * sizeof(int16_t);

        if (data_len + sizeof(MonoAudioPCMS16DataHeader) > max_len)
            return false;

        MonoAudioPCMS16DataHeader* header = (MonoAudioPCMS16DataHeader*)dst;

        int32_t max_id_len = sizeof(header->audio_input_id);

        strncpy_s(header->audio_input_id, max_id_len, audio_input_id.c_str(), audio_input_id.size());
        header->audio_input_id_len = audio_input_id.size() < max_id_len ? audio_input_id.size() : max_id_len;
        header->nb_sample_count = nb_sample_count;
        header->timestamp_ns = timestamp_ns;
        header->data_len = data_len;

        char* data_ptr = dst + sizeof(MonoAudioPCMS16DataHeader);

        memcpy(data_ptr, pcm_s16_data, data_len);

        return true;
    
    }, true);
}
} // namespace sdk_helper