﻿#pragma once

#include <optional>
#include <variant>
#include <set>
#include "LSExport.h"
#include "LSInternal.h"
#include "MediaSDK_Server.h"

#define EPS 1e-3

enum LAYER_CONTROL_CMD : UINT64
{
    LAYER_CONTROL_NONE = 0,
    <PERSON>YER_CONTROL_SET_SHOW = 1ULL << 0,
    LAYER_CONTROL_SET_TARGET_SIZE = 1ULL << 1,
    LAYER_CONTROL_SET_LAYOUT = 1ULL << 2,
    LAYER_CONTROL_SET_LOCK = 1ULL << 3,
    LAYER_CONTROL_SET_FLIPV = 1ULL << 4,
    LAYER_CONTROL_SET_FLIPH = 1ULL << 5,
    LAYER_CONTROL_SET_TRANSLATE = 1ULL << 6,
    LAYER_CONTROL_SET_ROTATE = 1ULL << 7,
    LAYER_CONTROL_SET_SCALE = 1ULL << 8,
    LAYER_CONTROL_SET_MIN_SCALE = 1ULL << 9,
    LAYER_CONTROL_SET_MAX_SCALE = 1ULL << 10,
    LAYER_CONTROL_SET_CLIP = 1ULL << 11,
    LAYER_CONTROL_SELECT = 1ULL << 12,
    LAYER_CONTROL_SET_MOVE_RANGE = 1ULL << 13,
    LAYER_CONTROL_SET_REF_LAYOUT = 1ULL << 14,
    LAYER_CONTROL_SET_VISUAL_FLAGS = 1ULL << 15,
    LAYER_CONTROL_SET_PREPARE_CLIP = 1ULL << 16,
    LAYER_CONTROL_SET_CLIP_MASK = 1ULL << 17,
    LAYER_CONTROL_SET_MOVE_TRANSITION = 1ULL << 18,
};

enum SOURCE_CONTROL_CMD
{
    SOURCE_CONTROL_NONE = 0,
    SOURCE_CONTROL_SET_OPTION = 1ULL << 0,
    SOURCE_CONTROL_CHANGE_WINDOW = 1ULL << 1,
    SOURCE_CONTROL_SEEK = 1ULL << 2,
    SOURCE_CONTROL_LOOP = 1ULL << 3,
    SOURCE_CONTROL_RESUME = 1ULL << 4,
    SOURCE_CONTROL_SET_FOCUS_CPU = 1ULL << 5,
    SOURCE_CONTROL_SET_LIMIT_RATE = 1ULL << 6,
    SOURCE_CONTROL_SET_CURSOR = 1ULL << 7,
    SOURCE_CONTROL_SET_VIDEO_PROCAMP = 1ULL << 8,
    SOURCE_CONTROL_SET_CAMERA_CONTROL = 1ULL << 9,
    SOURCE_CONTROL_PAUSE = 1ULL << 10,
    SOURCE_CONTROL_SET_DETECT_COLOR_RANGE = 1ULL << 11,
    SOURCE_CONTROL_STOP_DETECT_COLOR_RANGE = 1ULL << 12,
    SOURCE_CONTROL_SET_CAPTURE_MAX_RATE = 1ULL << 13,
    SOURCE_CONTROL_SET_EDIT_STATE = 1ULL << 14,
    SOURCE_CONTROL_SET_TYPE = 1ULL << 15,
    SOURCE_CONTROL_SET_PEN = 1ULL << 16,
};

enum SOURCE_INFO_CMD : UINT64
{
    SOURCE_INFO_NONE = 0,
    SOURCE_INFO_PAUSED = 1ULL << 0,
    SOURCE_INFO_MEDIA_SEEKABLE = 1ULL << 1,
    SOURCE_INFO_MEDIA_DURATION = 1ULL << 2,
    SOURCE_INFO_HAS_TRACKERS = 1ULL << 3,
    SOURCE_INFO_HAS_RESTORES = 1ULL << 4,
    SOURCE_INFO_EDITABLE = 1ULL << 5,
    SOURCE_INFO_CAP_PROC_AMP = 1ULL << 6,
    SOURCE_INFO_CAMERA_CONTROL = 1ULL << 7,
    SOURCE_INFO_FILTER_STATE = 1ULL << 8,
    SOURCE_INFO_FPS = 1ULL << 9,
};

enum AUDIO_CONTROL_CMD : UINT64
{
    AUDIO_CONTROL_NONE = 0,
    AUDIO_CONTROL_SET_ALL_SETTING = 1ULL << 0,
    AUDIO_CONTROL_SET_VOLUME = 1ULL << 1,
    AUDIO_CONTROL_SET_BALANCEING = 1ULL << 2,
    AUDIO_CONTROL_SET_SYNC_OFFSET = 1ULL << 3,
    AUDIO_CONTROL_SET_INTERVAL = 1ULL << 4,
    AUDIO_CONTROL_SET_MONITOR_TYPE = 1ULL << 5,
    AUDIO_CONTROL_SET_DOWN_MIX_MONO = 1ULL << 6,
    AUDIO_CONTROL_SET_MUTE = 1ULL << 7,
    AUDIO_CONTROL_UPDATE_PCM = 1ULL << 8,
    AUDIO_CONTROL_SET_AUDIO_TRACK = 1ULL << 9,
    AUDIO_CONTROL_SET_AEC_REF_ID = 1ULL << 10,
    AUDIO_CONTROL_ENABLE_AEC = 1ULL << 11,
    AUDIO_CONTROL_SET_AGC_OPTION = 1ULL << 12,
    AUDIO_CONTROL_SET_ANS_OPTION = 1ULL << 13,
};

enum AUDIO_INFO_CMD : UINT64
{
    AUDIO_INFO_NONE = 0,
    AUDIO_INFO_SETTING = 1ULL << 0,
    AUDIO_INFO_CAPTURE = 1ULL << 1,
};

enum FILTER_CONTROL_CMD : UINT64
{
    FILTER_CONTROL_NONE = 0,
    FILTER_CONTROL_ADD_COMPOSER = 1ULL << 0,
    FILTER_CONTROL_REMOVE_COMPOSER = 1ULL << 1,
    FILTER_CONTROL_SET_COMPOSER = 1ULL << 2,
    FILTER_CONTROL_UPDATE_COMPOSER = 1ULL << 3,
    FILTER_CONTROL_REPLACE_COMPOSERS = 1ULL << 4,
    FILTER_CONTROL_SET_COMPOSER_TEXT = 1ULL << 5,
    FILTER_CONTROL_SET_BACKGROUND_IMAGE = 1ULL << 6,
    FILTER_CONTROL_SET_SUPPRESS_LEVEL = 1ULL << 7,
    FILTER_CONTROL_SET_SPEECH_RATIO = 1ULL << 8,
    FILTER_CONTROL_SET_MODEL = 1ULL << 9,
    FILTER_CONTROL_SET_EDGE = 1ULL << 10,
    FILTER_CONTROL_SET_CORNER = 1ULL << 11,
    FILTER_CONTROL_SET_OVERLAY = 1ULL << 12,
    FILTER_CONTROL_SET_BRIGHT_CONFIG = 1ULL << 13,
    FILTER_CONTROL_SET_EFFECT_MSG = 1ULL << 14,
    FILTER_CONTROL_RESET_COMMON_METRICS = 1ULL << 15,
    FILTER_CONTROL_SET_MDSP_PARAM = 1ULL << 16,
    FILTER_CONTROL_SET_FILTER_ENABLE = 1ULL << 17,
    FILTER_CONTROL_SET_COLOR_SATURATION = 1ULL << 18,
    FILTER_CONTROL_SET_COLOR_HUE_SHIFT = 1ULL << 19,
    FILTER_CONTROL_SET_COLOR_ADD_COLOR = 1ULL << 20,
    FILTER_CONTROL_SET_COLOR_MUL_COLOR = 1ULL << 21,
    FILTER_CONTROL_SET_COLOR_BRIGHTNESS = 1ULL << 22,
    FILTER_CONTROL_SET_COLOR_GAMMA = 1ULL << 23,
    FILTER_CONTROL_SET_COLOR_CONTRAST = 1ULL << 24,
    FILTER_CONTROL_SET_COLOR_OPACITY = 1ULL << 25,
    FILTER_CONTROL_SET_COLOR_CHROMA = 1ULL << 26,
    FILTER_CONTROL_SET_COLOR_SIMILARITY = 1ULL << 27,
    FILTER_CONTROL_SET_COLOR_SMOOTHNESS = 1ULL << 28,
    FILTER_CONTROL_SET_COLOR_SPILL = 1ULL << 29,
    FILTER_CONTROL_SET_HINT = 1ULL << 30,
    FILTER_CONTROL_SET_COLOR_LUT = 1ULL << 31,
    FILTER_CONTROL_SET_SHARPNESS = 1ULL << 32,
    FILTER_CONTROL_SET_SCALE = 1ULL << 33,
    FILTER_CONTROL_SET_SHAPE = 1ULL << 34,
};

enum FILTER_INFO_CMD : UINT64
{
    FILTER_INFO_NONE = 0,
    FILTER_INFO_SUPPRESS_LEVEL = 1ULL << 2,
    FILTER_INFO_COMPOSER_EXCLUSION = 1ULL << 3,
    FILTER_INFO_GET_ENABLE = 1ULL << 4,
};

struct PARFAIT_PARAM
{
    bool         overseas = false;
    INT32        aid = 0;
    INT32        reportInterval = 30000;
    std::wstring dllPath = L"";
    std::string  did = "";
    std::string  uid = "";
    std::string  url = "";
    std::string  version = "";
    std::string  osversion = "";
    std::string  rootPathName = "";
    std::string  prefix = "";
    std::string  processName = "";
    std::string  host = "";
};

enum ENUM_OBJECTTYPE
{
    OBJECTTYPE_SCENE,
    OBJECTTYPE_CANVAS,
    OBJECTTYPE_LAYER,
    OBJECTTYPE_PREVIEW,
    OBJECTTYPE_FRAME,
    OBJECTTYPE_MAX,
};

enum VISUAL_TYPE
{
    VISUAL_NONE = 0,
    VISUAL_WINDOW,
    VISUAL_GAME,
    VISUAL_MONITOR,
    VISUAL_FAV,
    VISUAL_IMAGE,
    VISUAL_CAMERA,
    VISUAL_ANALOG,
    VISUAL_BROWSER,
    VISUAL_BYTELINK,
    VISUAL_GRAFFITI,
    VISUAL_RTC,
    VISUAL_VIRTUAL_CAMERA,
};

enum VISUAL_FLAG
{
    VISUAL_FLAG_NO_FLAG = 0,
    VISUAL_FLAG_SUCCESS_STATE,
    VISUAL_FLAG_VISIBLE_STATE,
    VISUAL_FLAG_ACTIVE_STATE,
    VISUAL_FLAG_LOCK_STATE,
    VISUAL_FLAG_SELECT_STATE,
    VISUAL_FLAG_HITTEST_STATE,
    VISUAL_FLAG_FLAG_SYNC,
    VISUAL_FLAG_FLAG_ASYNC,
    VISUAL_FLAG_OUTPUT_FILTER,
    VISUAL_FLAG_RENDER_FILTER,
    VISUAL_FLAG_RTC_SCREEN_FILTER,
    VISUAL_FLAG_RTC_OUTPUT,
    VISUAL_FLAG_FLAG_NOT_PREVIEW_BUT_OUTPUT,
    VISUAL_FLAG_ALWAYS_TOP,
    VISUAL_FLAG_AVOID_DESTROY_ALL,
    VISUAL_FLAG_RTC_NOT_OUTPUT,
    VISUAL_FLAG_RTC_ADD_TO_OUTPUT,
    VISUAL_FLAG_RTC_NOT_OUTPUT_TO_SCREEN,
    VISUAL_FLAG_RTC_ADD_TO_OUTPUT_SCREEN,
};

enum COLOR_SPACE
{
    COLOR_SPACE_UNSPECIFIED = 0,
    COLOR_SPACE_BT709 = 1,
    COLOR_SPACE_BT601 = 2,
    COLOR_SPACE_FCC = 3,
    COLOR_SPACE_SMPTE240M = 4,
    COLOR_SPACE_BT470 = 5,
    COLOR_SPACE_BT2020 = 6, // HDR
    COLOR_SPACE_BT2100 = 7, // HDR10
    COLOR_SPACE_MAX = COLOR_SPACE_BT2100,
};

enum COLOR_TRANSFER
{
    COLOR_TRANSFER_UNSPECIFIED = 0,
    COLOR_TRANSFER_LINEAR = 1,
    COLOR_TRANSFER_IEC61966_2_1 = 2, // SRGB
    COLOR_TRANSFER_GAMMA22 = 3,
    COLOR_TRANSFER_GAMMA28 = 4,
    COLOR_TRANSFER_SMPTE170M = 5,     // BT601
    COLOR_TRANSFER_SMPTE240M = 6,     // BT601
    COLOR_TRANSFER_BT709 = 7,         // BT709
    COLOR_TRANSFER_BT2020_10 = 8,     // BT2010 10BIT
    COLOR_TRANSFER_BT2020_12 = 9,     // BT2010 12BIT
    COLOR_TRANSFER_SMPTE2084 = 10,    // BT2100-PQ
    COLOR_TRANSFER_ARIB_STD_B67 = 11, // BT2100-HLG
};

enum COLOR_RANGE
{
    COLOR_RANGE_UNSPECIFIED = 0,
    COLOR_RANGE_PARTIAL = 1,
    COLOR_RANGE_FULL = 2,
    COLOR_RANGE_MAX = COLOR_RANGE_FULL,
};

struct CLIPF
{
    float x = .0f;
    float y = .0f;
    float z = .0f;
    float w = .0f;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "[" << x << "," << y << "," << z << "," << w << "]";
        return ss.str();
    }
};

struct TRANSFORM
{
    bool            hFlip = false;
    bool            vFlip = false;
    float           angle = .0f;
    Gdiplus::PointF scale{ 1.0f, 1.0f };
    Gdiplus::PointF translate{ .0f, .0f };
    CLIPF           clipRange{ .0f, .0f, .0f, .0f };
    Gdiplus::SizeF  size{ .0f, .0f };
    Gdiplus::PointF minScale{0.0f, 0.0f};
    Gdiplus::PointF maxScale{ 0.0f, 0.0f };

    std::string toString() const
    {
        std::stringstream ss;
        ss << "hFlip=" << hFlip << ",vFlip=" << vFlip << ",angle=" << angle << ",scale=[" << scale.X << "," << scale.Y << "],minScale=[" << minScale.X << "," << minScale.Y << "],maxScale=[" << maxScale.X << "," << maxScale.Y << "],translate=[" << translate.X << "," << translate.Y << "],clipRange:" << clipRange.toString() << ",size=[" << size.Width << "," << size.Height << "]";
        return ss.str();
    }
};

struct COLOR_CONFIG
{
    COLOR_SPACE    colorSpace = COLOR_SPACE_UNSPECIFIED;
    COLOR_TRANSFER colorTransfer = COLOR_TRANSFER_UNSPECIFIED;
    COLOR_RANGE    colorRange = COLOR_RANGE_UNSPECIFIED;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "colorSpace=" << colorSpace << ",colorTransfer=" << colorTransfer << ",colorRange=" << colorRange;
        return ss.str();
    }
};

enum VIDEO_PIXEL_FORMAT
{
    PIXEL_FORMAT_UNKNOWN = 0,
    PIXEL_FORMAT_I420 = 1,
    PIXEL_FORMAT_YV12 = 2,
    PIXEL_FORMAT_NV12 = 3,
    PIXEL_FORMAT_NV21 = 4,
    PIXEL_FORMAT_UYVY = 5,
    PIXEL_FORMAT_YUY2 = 6,
    PIXEL_FORMAT_ARGB = 7,
    PIXEL_FORMAT_XRGB = 8,
    PIXEL_FORMAT_RGB24 = 9,
    PIXEL_FORMAT_RGBA = 10,
    PIXEL_FORMAT_BGR24 = 11,
    PIXEL_FORMAT_BGRA = 12,
    PIXEL_FORMAT_MJPEG = 13,
    PIXEL_FORMAT_I444 = 14,
    PIXEL_FORMAT_I444A = 15,
    PIXEL_FORMAT_I420A = 16,
    PIXEL_FORMAT_I422 = 17,
    PIXEL_FORMAT_I422A = 18,
    PIXEL_FORMAT_YVYU = 19,
    PIXEL_FORMAT_P010 = 20,
    PIXEL_FORMAT_P016 = 21,
    PIXEL_FORMAT_NV12_MS = 22,
    PIXEL_FORMAT_P010_MS = 23,
    PIXEL_FORMAT_P016_MS = 24,
    PIXEL_FORMAT_I010 = 25,
    PIXEL_FORMAT_V210 = 26,
    PIXEL_FORMAT_I210 = 27,
    PIXEL_FORMAT_HDYC = 28,
    PIXEL_FORMAT_MAX = PIXEL_FORMAT_HDYC,
};

enum LIVE_MODE
{
    LIVE_MODE_LANDSCAPE,
    LIVE_MODE_PORTRAIT,
    LIVE_MODE_DBCANVAS,
    LIVE_MODE_MAX,
};

struct INITIALIZE_INFO
{
    HWND        bottomWnd = NULL;
    HWND        topWindow = NULL;
    HINSTANCE   hi = NULL;
    SIZE        outputSize{0, 0};
    float       fps = 60.0f;
    std::string json = "";
    std::string workDir = "";
    std::string resDir = "";
    std::string storeDir = "";
    std::string version = "";
    std::string uid = "";
    std::string did = "";
    std::string host = "";
    std::string sdkLogFile = "";
    bool        asyncLog = false;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "bottomWnd=" << bottomWnd << ",topWindow=" << topWindow << ",hinstance=" << hi << ",outputSize=[" << outputSize.cx << "," << outputSize.cy << "],fps=" << fps << ",version=" << version << ",uid=" << uid << ",did=" << did << ",host=" << host << ",workDir=" << workDir << ",resDir=" << resDir << ", asyncLog=" << asyncLog;
        return ss.str();
    }
};

struct VideoParam
{
    float output_size_width;
    float output_size_height;
    bool  focus_nv12;
    float fps;

    COLOR_SPACE    color_space;
    COLOR_TRANSFER color_transfer;
    COLOR_RANGE    color_range;
};

struct SetVideoSettingParam
{
    std::vector<int32_t>    track_id_list;
    std::vector<VideoParam> video_param_list;
};

struct MODE_INFO
{
    UINT64 id = LIVE_MODE_MAX;
};

struct SCENE_INFO
{
	LIVE_MODE mode = LIVE_MODE_MAX;
	UINT64    id = 0;
};

enum FILTER_TYPE
{
    FILTER_NONE = 0,
    FILTER_AUDIO,
    FILTER_VISUAL,
    FILTER_EFFECT,
    FILTER_CANVAS,
};

enum AUDIO_FILTER_TYPE
{
    AUDIO_FILTER_NONE = 0,
    AUDIO_FILTER_SPEEX_NOISE_SUPPRESS,
    AUDIO_FILTER_SAMI_NOISE_SUPPRESS,
    AUDIO_FILTER_SAMI_COMMON_METRICS,
    AUDIO_FILTER_SAMI_MDSP_EFFECT,
};

struct SPEEX_NOISE_SUPPRESS_FILTER
{
    INT32 suppressLevel = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "suppressLevel=" << suppressLevel;
        return ss.str();
    }
};

struct SAMI_NOISE_SUPPRESS_FILTER
{
    std::string configFile = "";
    float       speechRatio = .0f;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "configFile=" << configFile << ",speechRatio=" << speechRatio;
        return ss.str();
    }
};

struct SAMI_COMMON_METRICS_FILTER
{
    std::string configFile = "";
    std::string configJson = "";

    std::string toString() const
    {
        std::stringstream ss;
        ss << "configFile=" << configFile << ",configJson=" << configJson;
        return ss.str();
    }
};

struct SAMI_MDSP_EFFECT_FILTER
{
    std::string modelFile = "";
    std::string resFilePath = "";
    std::string mdspParam = "";

    std::string toString() const
    {
        std::stringstream ss;
        ss << "modelFile=" << modelFile << ",resFilePath=" << resFilePath << ",mdspParam=" << mdspParam;
        return ss.str();
    }
};

struct AUDIO_FILTER
{
    AUDIO_FILTER_TYPE           filterType = AUDIO_FILTER_NONE;
    SPEEX_NOISE_SUPPRESS_FILTER speexNoiseSuppressFilter{};
    SAMI_NOISE_SUPPRESS_FILTER  samiNoiseSuppressFilter{};
    SAMI_COMMON_METRICS_FILTER  samiCommonMetricsFilter{};
    SAMI_MDSP_EFFECT_FILTER     samiMdspEffectFilter{};
};

enum VISUAL_FILTER_TYPE
{
    VISUAL_FILTER_NONE = 0,
    VISUAL_FILTER_CORNER = 1,
    VISUAL_FILTER_EDGE = 2,
    VISUAL_FILTER_OVERLAY = 3,
    VISUAL_FILTER_COLOR_ADJUST = 4,
    VISUAL_FILTER_CHROMA_KEY = 5,
    VISUAL_FILTER_HINT = 6,
    VISUAL_FILTER_COLOR_LUT = 7,
    VISUAL_FILTER_SHARPNESS = 8,
    VISUAL_FILTER_SCALE = 9,
    VISUAL_FILTER_SHAPE = 10
};

struct ShapeFilter
{
    float startPoint = 0.0f;
    std::string toString() const
    {
        std::stringstream ss;
        ss << "startPoint=" << startPoint;
        return ss.str();
    }
};

struct POINT_NINE_INFO
{
    bool            isBorder = false;
    Gdiplus::PointF left = { .0f, .0f };
    Gdiplus::PointF top = { .0f, .0f };

    std::string toString() const
    {
        std::stringstream ss;
        ss << "isBorder=" << isBorder << ",left=[" << left.X << "," << left.Y << "],top=[" << top.X << "," << top.Y << "]";
        return ss.str();
    }
};

struct EdgeFilter
{
    CLIPF edgeColor{};
    float edgeWidth = .0f;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "edgeColor:" << edgeColor.toString() << ",edgeWidth=" << edgeWidth;
        return ss.str();
    }
};

struct CornerFilter
{
    CLIPF cornerInfo{};
    bool  fixedRadius = false;
    bool  referenceBorderWidth = false;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "cornerInfo:" << cornerInfo.toString() << ",fixedRadius=" << fixedRadius << ",referenceBorderWidth=" << referenceBorderWidth;
        return ss.str();
    }
};

struct OverlayFilter
{
    std::string     bkImage = "";
    float           bkScale = 1.0f; // the background size can biggger or smaller than visual. Scale = size_of_visual / size_of_background
    POINT_NINE_INFO pointNine{};    // if the image is .9 or want to treat it as .9 image, the object must not be null

    std::string toString() const
    {
        std::stringstream ss;
        ss << "bkImage=" << bkImage << ",bkScale=" << bkScale << ",pointNine:" << pointNine.toString();
        return ss.str();
    }
};

struct ColorAdjustFilter
{
    float brightness = .0f;         // [-100.0, 100.0]
    float saturation = .0f;         // [-100.0, 100.0]
    float gamma = .0f;              // [-3.0, 3.0]
    float contrast = .0f;           // [-100.0, 100.0]
    float hueShift = .0f;           // [-180.0, 180.0]
    float opacity = 100.0f;         // [0.0, 100.0]
    UINT32 addColor = 0;            // [0, 0x00FFFFFF]
    UINT32 mulColor = 0x00FFFFFF;   // [0, 0x00FFFFFF]

    std::string toString() const
    {
        std::stringstream ss;
        ss << "brightness=" << brightness << ",saturation=" << saturation << ",gamma=" << gamma << ",contrast=" << contrast << ",hueShift=" << hueShift << ",opacity=" << opacity;
        return ss.str();
    }
};

struct ChromaKeyFilter
{
    UINT32 chroma = 0x0000FF00; // [0, 0x00FFFFFF]
    float  similarity = 35.0;   // [0.0, 100.0]
    float  smoothness = 8.0;    // [0.0, 100.0]
    float  spill = 10.0;        // [0.0, 100.0]
    float  opacity = 100.0f;    // [0.0, 100.0]
    float  gamma = .0f;         // [-3.0, 3.0]
    float  contrast = .0f;      // [-100.0, 100.0]
    float  brightness = .0f;    // [-100.0, 100.0]

    std::string toString() const
    {
        std::stringstream ss;
        ss << "brightness=" << brightness << ",saturation=" << ",gamma=" << gamma << ",contrast=" << contrast << ",opacity=" << opacity << ",chroma=" << chroma << ",similarity=" << similarity << ",smoothness=" << smoothness << ",spill=" << spill;
        return ss.str();
    }
};

struct HintFilter
{
    UINT32      gap;
    UINT32      imageWidth;
    UINT32      imageHeight;
    UINT32      animationIterationCnt;
    UINT32      textWidth;
    UINT32      fontSize;
    std::string imagePath;
    std::string text;
    std::string fontFamily;
    std::string fontFilePath;
    std::string fontColor;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "gap=" << gap << ",imageWidth=" << imageWidth << ",imageHeight=" << imageHeight << ",animationIterationCnt=" << animationIterationCnt << ",textWidth=" << textWidth << ",fontSize=" << fontSize << ",imagePath=" << imagePath << ",text=" << text << ",fontFamily=" << fontFamily << ",fontFilePath=" << fontFilePath << ",fontColor=" << fontColor;
        return ss.str();
    }
};

struct ScaleFilter
{
    float startPoint;
    float ratio;
    std::string toString() const
    {
        std::stringstream ss;
        ss << "startPoint=" << startPoint << ", ratio=" << ratio;
        return ss.str();
    }
};

struct ColorLutFilter
{
    std::optional<std::string> file_path;
    std::optional<float> amount;
    std::optional<bool>  pass_through_alpha;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "file_path = " << (file_path.has_value() ? file_path.value() : "") << ",amount = " << (amount.has_value() ? amount.value() : 0) << ",pass_through_alpha = " << (pass_through_alpha.has_value() ? pass_through_alpha.value() : 0);
        return ss.str();
    }
};

struct SharpnessFilter
{
    float sharpness;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "sharpness = " << sharpness;
        return ss.str();
    }
};

struct VISUAL_FILTER
{
    VISUAL_FILTER_TYPE filterType = VISUAL_FILTER_NONE;
    EdgeFilter         edgeFilter{};
    CornerFilter       cornerFilter{};
    OverlayFilter      overlayFilter{};
    ColorAdjustFilter  colorAdjustFilter{};
    ChromaKeyFilter    chromaKeyFilter{};
    HintFilter         hintFilter{};
    ScaleFilter        scaleFilter{};
    ShapeFilter        shapeFilter{};
    ColorLutFilter     colorLutFilter{};
    SharpnessFilter    sharpnessFilter{};
};

struct MAP_INFO
{
    std::string key = "";
    std::string val = "";
};

struct BRIGHT_CONFIG
{
    bool        enable = false;
    bool        isAuto = false;
    std::string assetPath = "";
    MAP_INFO    keyVal{};
};

struct EFFECT_COMPOSER
{
    std::string effectPath = "";
    MAP_INFO    keyVal{};
    std::string composerTag = "";

    std::string toString() const
    {
        std::stringstream ss;
        ss << "effectPath=" << effectPath << ",key=" << keyVal.key << ",val=" << keyVal.val << ",composerTag=" << composerTag;
        return ss.str();
    }
};

struct EFFECT_MSG
{
    UINT64 msgID = 0;
    INT64 arg1 = 0;
    INT64 arg2 = 0;
    std::string arg3 = "";
};

struct EFFECT_FILTER
{
    std::vector<EFFECT_COMPOSER> composers{}; // composers all, for create and reset
    std::vector<EFFECT_COMPOSER> addComposers{};
    std::vector<EFFECT_COMPOSER> removeComposers{};
    std::vector<EFFECT_COMPOSER> oldComposers{};
    std::vector<EFFECT_COMPOSER> newComposers{};
    EFFECT_COMPOSER              updateComposer{};
    BRIGHT_CONFIG                brightConfig{};
    EFFECT_MSG                   effectMsg{};
    MAP_INFO                     keyText{};
    MAP_INFO                     pathTag{};
    MAP_INFO                     keyPath{};
    INT32                        exclusion = 0;
    UINT32                       effectFps = 0;
	float                        achieveRate = .0f;
	bool                         result = false;
};

enum TRANSITION_TYPE
{
    TRANSITION_TYPE_NONE = 0,
    TRANSITION_TYPE_SLIDE = 1,
    TRANSITION_TYPE_SWIPE = 2,
    TRANSITION_TYPE_CARTOON = 3,
    TRANSITION_TYPE_FADE = 4,
    TRANSITION_TYPE_FADE2COLOR = 5,
    TRANSITION_TYPE_LUMINANCE_WIDE = 6,
    TRANSITION_TYPE_MOVE = 7,
};

enum TRANSITION_DIRECTION
{
    TRANSITION_DIRECTION_LEFT = 0,
    TRANSITION_DIRECTION_RIGHT = 1,
    TRANSITION_DIRECTION_UP = 2,
    TRANSITION_DIRECTION_DOWN = 3,
};

enum TRANSITION_PROGRESS_FUNCTION_TYPE
{
    TRANSITION_PROGRESS_FUNCTION_LINEAR = 0,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_SINE = 1,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_SINE = 2,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_SINE = 3,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUAD = 4,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUAD = 5,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUAD = 6,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_CUBIC = 7,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_CUBIC = 8,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CUBIC = 9,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUART = 10,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUART = 11,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUART = 12,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUINT = 13,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUINT = 14,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUINT = 15,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_EXPO = 16,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_EXPO = 17,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_EXPO = 18,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_CIRC = 19,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_CIRC = 20,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CIRC = 21,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_BACK = 22,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_BACK = 23,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_BACK = 24,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_ELASTIC = 25,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_ELASTIC = 26,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_ELASTIC = 27,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_BOUNCE = 28,
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_BOUNCE = 29,
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_BOUNCE = 30,
};

struct SlideTransition
{
    TRANSITION_DIRECTION              direction = TRANSITION_DIRECTION_LEFT;
    TRANSITION_PROGRESS_FUNCTION_TYPE func = TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CUBIC;
};

enum SWIPE_TYPE
{
    SWIPE_IN = 0,
    SWIPE_OUT = 1,
};

struct SwipeTransition
{
    TRANSITION_DIRECTION              direction = TRANSITION_DIRECTION_LEFT;
    SWIPE_TYPE                        swipeType = SWIPE_IN;
    TRANSITION_PROGRESS_FUNCTION_TYPE func = TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CUBIC;
};

struct CartoonTransition
{
    float       middleProgress = 0.5; // [.0f, 1.0f]
    std::string filePath = "";
};

struct FadeTransition
{
    TRANSITION_PROGRESS_FUNCTION_TYPE func = TRANSITION_PROGRESS_FUNCTION_LINEAR;
};

struct Fade2ColorTransition
{
    UINT32                            color = 0x000000;
    float                             middleProgress = 0.5; // [.0f, 1.0f]
    TRANSITION_PROGRESS_FUNCTION_TYPE func = TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CUBIC;
};

struct LuminanceWideTransition
{
    TRANSITION_PROGRESS_FUNCTION_TYPE func = TRANSITION_PROGRESS_FUNCTION_LINEAR;
    string                            filePath = "";
    bool                              invert = false;
    float                             softness = 0.5; // [.0f, 1.0f]
};

enum TRANSITION_MOVE_TYPE
{
    TRANSITION_MOVE_NONE = 0,
    TRANSITION_MOVE_SLIDE = 1,
    TRANSITION_MOVE_SCALE = 2,
    TRANSITION_MOVE_FADE = 3,
};

struct MOVE_TRANSITION_INFO
{
    TRANSITION_MOVE_TYPE              moveType = TRANSITION_MOVE_NONE;
    TRANSITION_PROGRESS_FUNCTION_TYPE moveInFunc = TRANSITION_PROGRESS_FUNCTION_EASE_IN_CUBIC;
    TRANSITION_PROGRESS_FUNCTION_TYPE moveOutFunc = TRANSITION_PROGRESS_FUNCTION_EASE_OUT_CUBIC;
    TRANSITION_PROGRESS_FUNCTION_TYPE move2Func = TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CUBIC;
    TRANSITION_DIRECTION              moveInFromDirection = TRANSITION_DIRECTION_LEFT;
    TRANSITION_DIRECTION              moveOut2Direction = TRANSITION_DIRECTION_RIGHT;
    Gdiplus::PointF                   moveInFromPos{.0f, .0f};
    Gdiplus::PointF                   moveOut2Pos{1.0f, 1.0f};
};

struct MoveTransition
{
    float middleProgress = 0.5; // [.0f, 1.0f]
    std::vector<std::pair<std::string, std::string>> matchedLayerIDs{};
    MOVE_TRANSITION_INFO                             moveTransitionInfo{};
};

struct TransitionFilter
{
    TRANSITION_TYPE         transitionType = TRANSITION_TYPE_NONE;
    UINT64                  durationMs = 500;
    SlideTransition         slideTransition{};
    SwipeTransition         swipeTransition{};
    CartoonTransition       cartoonTransition{};
    FadeTransition          fadeTransition{};
    Fade2ColorTransition    fade2ColorTransition{};
    LuminanceWideTransition luminanceWideTransition{};
    MoveTransition          moveTransition{};
};

enum CANVAS_FILTER_TYPE
{
    CANVAS_FILTER_NONE = 0,
    CANVAS_FILTER_TRANSITION = 1,
    CANVAS_FILTER_COLOR_ADJUST = 2,
};

struct CANVAS_FILTER
{
    CANVAS_FILTER_TYPE filterType = CANVAS_FILTER_NONE;
    TransitionFilter   transitionFilter{};
    ColorAdjustFilter  colorAdjustFilter{};
};

struct FILTER
{
    FILTER_TYPE         type = FILTER_NONE;
    UINT64              id = 0;
    std::optional<bool> enable = true;
    std::vector<UINT64> mediaIDs{};
    bool                isCreated = false;
    std::variant<AUDIO_FILTER, VISUAL_FILTER, EFFECT_FILTER, CANVAS_FILTER> filter;
};

struct FILTER_CONTROL_INFO
{
    FILTER_CONTROL_CMD cmd = FILTER_CONTROL_NONE;
    FILTER             filterInfo{};
};

struct INIT_EFFECT_PLATFORM
{
    std::string appVersion = "";
    std::string deviceType = "";
    std::string appID = "";
    std::string accessKey = "";
    std::string channel = "";
    std::string effectCacheDir = "";
    std::string modelCacheDir = "";
    std::string builtInModelDir = "";
    std::string lokiHost = "";        // for Tiktok Live Studio: https://api.tiktokv.com
    std::string veCloudHost = "";     // for Tiktok Live Studio: https://api.tiktokv.com
    std::string modelStatus = "";
    std::string region = "";
    std::string deviceID = "";
    std::string userID = "";
    std::string ttlsHardwareLevel = "";
};

struct CANVAS_INFO
{
    UINT64         sceneID = 0;
    UINT64         id = 0;
    UINT32         videoModel = UINT32_MAX;
    UINT32         desc = LIVE_MODE_MAX;
    HWND           hwnd = NULL;
    Gdiplus::RectF rect = {.0f, .0f, .0f, .0f};
    Gdiplus::RectF layoutRect = {.0f, .0f, .0f, .0f};
    Gdiplus::SizeF outputSize = {.0f, .0f};
    float          fps = 60.0f;
    UINT32         bkColor = 0;
    bool           allSync = false;
    bool           isCreated = false;
    FILTER         filter{};
};

struct DSHOW
{
    std::string id = "";
    std::string name = "";
    std::string exe = "";

    std::string toString() const
    {
        std::stringstream ss;
        ss << "id=" << id << ",name=" << name << ",exe=" << exe;
        return ss.str();
    }
};

enum AUDIO_TYPE
{
    AUDIO_NONE = 0,
    AUDIO_WAS,
    AUDIO_APP,
    AUDIO_PCM,
    AUDIO_VIS,
};

enum AUDIO_MONITOR_TYPE
{
    AUDIO_MONITOR_NONE = 0,
    AUDIO_MONITOR_STREAM,            // output to stream
    AUDIO_MONITOR_DEVICE,            // output to device
    AUDIO_MONITOR_STREAM_AND_DEVICE, // output to stream and device
};

enum AUDIO_INPUT_TYPE
{
    AUDIO_INPUT_NONE = 0,
    AUDIO_INPUT_MICROPHONE,
    AUDIO_INPUT_LOOPBACK,
    AUDIO_INPUT_ANALOG,
    AUDIO_INPUT_FAV,
    AUDIO_INPUT_RTC,
    AUDIO_INPUT_WIRELINE_IOS,
    AUDIO_INPUT_WIRELINE_ANDROID,
    AUDIO_INPUT_WIRELESS_IOS,
    AUDIO_INPUT_WIRELESS_ANDROID,
    AUDIO_INPUT_PCM,
    AUDIO_INPUT_FF,
    AUDIO_INPUT_BROWSER,
    AUDIO_INPUT_APP,
    AUDIO_INPUT_LYRAX,
};

enum WASAPI_AUDIO_ERROR_CODE
{
    WASAPI_AUDIO_SUCCESS = 0,
    AUDIO_DEVICE_ID_NULL,
    AUDIO_READY_EVENT_CREATE_FAILD,
    EXIT_EVENT_CREATE_FAILED,
    COCRAETEINSTANCE_API_FAILED,
    GET_DEVICE_FAILED,
    GET_STATE_FAILED,
    GET_AUDIO_CLIENT_FAILED,
    ACTIVE_CLIENT_FAILED,
    GET_MIX_FORMAT_FAILED,
    AUDIO_CLIENT_INIT_FIALED,
    TRY_CLOSEST_FORMAT_FAILED,
    ACTIVE_VOLUME_FAILED,
    SET_EVENT_HANDLE_FIALED,
    GET_SERVICE_FIALED,
    CLEAR_LOOPBACK_BUFFER_FIALED,
    AUDIO_CLIENT_START_FIALED,
};

enum APP_AUDIO_ERROR_CODE
{
    APP_AUDIO_SUCCESS = 0,
    INVALID_PID,
    ACTIVE_AUDIO_INTERFACE_NULLPTR,
    ACTIVE_AUDIO_INTERFACE_FAILED,
    CW_WAIT_FAILED,
    AUDIO_CLIENT_INIT_FAILED,
    SET_EVENT_HANDLER_FAILED,
    GET_SERVICE_FAILED,
    AUDIO_CLIENT_START_FAILED,
    PROCESS_DATA_FAILED,
    WAIT_DATA_TIMEOUT,
    FIRST_FRAME_CALLBACK,
};

enum PCM_AUDIO_ERROR_CODE
{
    PCM_AUDIO_SUCCESS = 0,
    CREATE_INVALID_LEFT_RIGHT_DATAS,
    UPDATE_JSON_PARAMS_NULL,
    UPDATE_JSON_PARAMS_EMPTY,
    UPDATE_JSON_PARAMS_INVALID,
    UPDATE_MISS_LEFT_RIGHT_DATAS,
    UPDATE_INVALID_LEFT_RIGHT_DATAS,
};

struct AUDIO_SETTING
{
    float              volume = 1.0f;                                 // audio volume after process
    float              balanceing = 0.5f;                             // audio balance factor, 0.0~0.1, default 0.5
    UINT32             syncOffset = 0;                                // audio timestamp offset to sync with video, in seconds
    UINT32             interval = 120;                                // audio interval time
    bool               downMixMono = false;                           // whether to mix all audio channels to mono
    bool               mute = false;                                  // whether to mute
    AUDIO_MONITOR_TYPE monitorType = AUDIO_MONITOR_STREAM;            // audio output type: 0：none，1：stream，2：devices，3: all

    std::string toString() const
    {
        std::stringstream ss;
        ss << "volume=" << volume << ",balanceing=" << balanceing << ",syncOffset=" << syncOffset << ",interval=" << interval << ",downMixMono=" << downMixMono << ",mute=" << mute << ",monitorType=" << monitorType;
        return ss.str();
    }
};

struct AUDIO_CAPTURE
{
    UINT32 samplePerSec = 44100;
    UINT32 bitsPerSec = 0;
    UINT32 channels = 2;
    UINT32 frames = 0;
    UINT32 planes = 0;
    UINT32 audioFormat = 8;   // 8: AUDIO_FORMAT_FLOAT_PLANAR
    UINT32 channelLayout = 2; // 2: CHANNEL_STEREO

    std::string toString() const
    {
        std::stringstream ss;
        ss << "samplePerSec=" << samplePerSec << ",bitsPerSec=" << bitsPerSec << ",channels=" << channels << ",frames=" << frames << ",planes=" << planes << ",audioFormat=" << audioFormat << ",channelLayout=" << channelLayout;
        return ss.str();
    }
};

enum BYPASS_SYSTEM_ENHANCEMENT_MODE
{
    ENHANCEMENT_CLOSE = 0,
    ENHANCEMENT_OPEN,
    ENHANCEMENT_AUTO,
};

enum MICROPHONE_BOOST_GAIN_PROPORTION
{
    PROPORTION_ORIGINAL = 0,
    PROPORTION_0,
    PROPORTION_25,
    PROPORTION_50,
    PROPORTION_75,
    PROPORTION_100,
};

struct LYRAX_ENGINE_INFO
{
    std::vector<std::string> accessHost{};
    std::string              logSdkWebsocketUrl = "";
    std::string              abLabel = "";
    std::string              appID = "";
    std::string              appVersion = "";
};

struct WAS_AUDIO
{
    AUDIO_INPUT_TYPE                 type = AUDIO_INPUT_NONE;
    bool                             isLyrax = false;
    float                            micInputLevel = .0f;
    BYPASS_SYSTEM_ENHANCEMENT_MODE   sysEnhancementMode = ENHANCEMENT_AUTO;
    MICROPHONE_BOOST_GAIN_PROPORTION micBoostLevel = PROPORTION_50;
    std::vector<std::string>         microphoneBoostPartnames{};

    std::string toString() const
    {
        std::stringstream ss;
        ss << "type=" << type << ",isLyrax=" << isLyrax << ",micInputLevel=" << micInputLevel << ",sysEnhancementMode=" << sysEnhancementMode << ",micBoostLevel=" << micBoostLevel;
        ss << ",microphoneBoostPartnames:";
        for (const auto str : microphoneBoostPartnames)
            ss << str << ",";
        return ss.str();
    }
};

struct APP_AUDIO
{
    bool excludePID = false;
};

struct PCM_AUDIO
{
    std::string bufLeft = "";
    std::string bufRight = "";
};

enum AUDIO_ANS_OPTION
{
    ANS_OPTION_CLOSE = 0,
    ANS_OPTION_LOW,
    ANS_OPTION_MEDIUM,
    ANS_OPTION_HIGH,
    ANS_OPTION_AUTO,
};

enum AUDIO_AGC_OPTION
{
    AGC_OPTION_AUTO = 0,
    AGC_OPTION_CLOSE = 1,
    AGC_OPTION_OPEN = 2,
};

struct ERASE_AUDIO_INFO
{
    UINT64 id = 0;
    UINT64 originAudioID = 0;
    UINT32 audioTrack = 0;
    UINT32 bufferTimeMs = 0;
    AUDIO_SETTING audioSetting{};
    std::string replacePath = "";
    bool enableAutoMute = false;
    bool isCreated = false;
};

struct AUDIO_INFO
{
    UINT64              id = 0;
    UINT64              eraseAudioID = 0;
    AUDIO_TYPE          type = AUDIO_NONE;
    UINT32              audioTrack = 0;
    UINT64              retryCount = 1;
    DSHOW               device{};
    AUDIO_SETTING       audioSetting{};
    AUDIO_CAPTURE       audioCapture{};
    std::string         aecRefID = "";
    std::vector<FILTER> filters{};
    bool                enableAec = false;
    AUDIO_AGC_OPTION    agcOption = AGC_OPTION_AUTO;
    AUDIO_ANS_OPTION    ansOption = ANS_OPTION_AUTO;
    bool                isCreated = false;

    // extra attributes
    std::variant<WAS_AUDIO, APP_AUDIO, PCM_AUDIO> audio;
};

struct AUDIO_CONTROL_INFO
{
    AUDIO_CONTROL_CMD cmd = AUDIO_CONTROL_NONE;
    AUDIO_INFO        audioInfo{};
    ERASE_AUDIO_INFO  eraseAudioInfo{};
};

struct WINDOW_DESC
{
    HWND        hwnd = NULL;
    DWORD       dwPID = 0;
    UINT32      width = 0;
    UINT32      height = 0;
    std::string szClass = "";
    std::string szEXE = "";
    std::string szTitle = "";
    std::string iconBase64 = "";
    bool        compatible = false;
    bool        hasCursor = false;
    bool        isMinimized = false;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "hwnd=" << hwnd << ",dwPID=" << dwPID << ",width=" << width << ",height=" << height << ",szClass=" << szClass << ",szEXE=" << szEXE << ",szTitle=" << szTitle << ",compatible=" << compatible << ",hasCursor=" << hasCursor << ",isMinimized=" << isMinimized;
        return ss.str();
    }
};

enum WINDOW_CAPTURE_TYPE
{
    WINDOW_CAPTURE_NONE = 0,
    WINDOW_CAPTURE_WINRT,
    WINDOW_CAPTURE_WIN7,
};

struct WINDOW_SOURCE
{
    WINDOW_CAPTURE_TYPE captureType = WINDOW_CAPTURE_NONE;
    WINDOW_DESC         windowDesc{};
    UINT32              implVer = 0;
    bool                bIcon = false;
    bool                forceWinrt = false;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "window desc:" << windowDesc.toString() << ",captureType=" << captureType << ",implVer=" << implVer << ",bIcon=" << bIcon << ",forceWinrt=" << forceWinrt;
        return ss.str();
    }
};

struct GAME_SOURCE
{
    WINDOW_DESC windowDesc{};
    UINT32      implVer = 0;
    bool        enableCpu = false;
    bool        enableLimit = false;
    float       errorReportTime = .0f;
    bool        forbidGameInject = false;
    float       limitFps = 30.0f;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "window desc:" << windowDesc.toString() << ",implVer=" << implVer << ",enableCpu" << enableCpu << ",enableLimit" << enableLimit << ",errorReportTime" << errorReportTime << ",forbidGameInject" << forbidGameInject;
        return ss.str();
    }
};

struct MATERIAL_DESC
{
    std::string    path = "";
    INT64          duration = 0;
    bool           seekable = false;
    bool           loop = false;
    bool           isGif = false;
    float          angle = .0f;
    Gdiplus::SizeF size{ .0f, .0f };

    std::string toString() const
    {
        std::stringstream ss;
        ss << "path=" << path << ",duration=" << duration << ",seekable=" << seekable << ",loop=" << loop << "isGif=" << isGif << ",angle=" << angle << ",size.width=" << size.Width << ",size.height=" << size.Height;
        return ss.str();
    }
};

struct IMAGE_SOURCE
{
    MATERIAL_DESC materialDesc{};

    std::string toString() const
    {
        std::stringstream ss;
        ss << "material desc:" << materialDesc.toString();
        return ss.str();
    }
};

struct FAV_SOURCE
{
    MATERIAL_DESC           materialDesc{};
    std::string             favSei = "";
    UINT32                  bufferSize = 0;
    float                   seekTime = .0f;
    bool                    enableHardDecode = false;
    std::optional<uint32_t> readPacketTimeoutMS;
    std::optional<bool>     disablePostDriverTask;
	bool                    absolute = false;
	bool                    paused = false;
	COLOR_CONFIG            colorConfig{};
	UINT32                  audioTrack = 0;
	AUDIO_SETTING           audioSetting{};
    std::optional<std::string> audioRenderDeviceId;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "material desc:" << materialDesc.toString() << ",favSei=" << favSei << ",bufferSize=" << bufferSize << ",seekTime=" << seekTime << ",enableHardDecode=" << enableHardDecode << ",absolute=" << absolute << ",paused=" << paused << ",colorConfig:" << colorConfig.toString() << ",audioTrack=" << audioTrack << ",audioSetting:" << audioSetting.toString();
        if (readPacketTimeoutMS.has_value())
            ss << " read_packet_time_out_ms = " << readPacketTimeoutMS.value();
        if (disablePostDriverTask.has_value())
            ss << " disable_post_driver_task = " << disablePostDriverTask.value();
        if (audioRenderDeviceId.has_value())
            ss << " audio_render_device_id = " << audioRenderDeviceId.value();
        return ss.str();
    }
};

struct BROWSER_SOURCE
{
    std::string token = "";
    std::string captureDLL = "";
    UINT32      deviceID = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "token=" << token << ",captureDLL=" << captureDLL << ",deviceID=" << deviceID;
        return ss.str();
    }
};

struct USER_MONITOR_INFO
{
    RECT                     windowRect{};
    std::vector<std::string> monitors{};
    std::vector<std::string> allMonitorDidList{};
};

struct MONITOR_DESC
{
    Gdiplus::RectF rect{ .0f, .0f, .0f, .0f };
    std::string    did = "";
    UINT32         index = 0;
    UINT32         degree = 0;

    string toString() const
    {
        std::stringstream ss;
        ss << "index=" << index << ",did=" << did << ",degree=" << degree << ",rect=[" << rect.X << "," << rect.Y << "," << rect.Width << "," << rect.Height << "]";
        return ss.str();
    }
};

enum MONITOR_TYPE
{
    MONITOR_NONE = 0,
    MONITOR_DUPLICATOR,
    MONITOR_WINRT,
    MONITOR_WINDOW7,
};

struct MONITOR_SOURCE
{
    MONITOR_DESC monitorDesc{};
    MONITOR_TYPE monitorType = MONITOR_NONE;
    UINT32       implVer = 0;
    bool         hasCursor = false;

    string toString() const
    {
        std::stringstream ss;
        ss << "monitor desc:" << monitorDesc.toString() << ",monitorType=" << monitorType << ",implVer=" << implVer << ",hasCursor=" << hasCursor;
        return ss.str();
    }
};

struct CAPTURE_ATTR
{
    INT32 min = 0;
    INT32 max = 0;
    INT32 step = 0;
    INT32 defVal = 0;
    INT32 flag = 0;
    INT32 value = 0;
    INT32 currentFlag = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "min=" << min << ",max=" << max << ",step=" << step << ",defVal=" << defVal << ",flag=" << flag << ",value=" << value << ",currentFlag=" << currentFlag;
        return ss.str();
    }
};

enum VIDEO_PROCAMP_TYPE
{
    VIDEO_PROCAMP_NONE = 0,
    VIDEO_PROCAMP_BRIGHTNESS,
    VIDEO_PROCAMP_CONTRAST,
    VIDEO_PROCAMP_HUE,
    VIDEO_PROCAMP_SATURATION,
    VIDEO_PROCAMP_SHARPNESS,
    VIDEO_PROCAMP_GAMMA,
    VIDEO_PROCAMP_COLORENABLE,
    VIDEO_PROCAMP_WHITEBALANCE,
    VIDEO_PROCAMP_BACKLIGHTCOMPENSATION,
    VIDEO_PROCAMP_GAIN,
};

enum CAMERA_CONTROL_TYPE
{
    CAMERA_CONTROL_NONE = 0,
    CAMERA_CONTROL_PAN,
    CAMERA_CONTROL_TILT,
    CAMERA_CONTROL_ROLL,
    CAMERA_CONTROL_ZOOM,
    CAMERA_CONTROL_EXPOSURE,
    CAMERA_CONTROL_IRIS,
    CAMERA_CONTROL_FOCUS,
};

enum CAMERA_FILTER_STATE
{
    CAMERA_FILTER_STATE_STOPPED = 0,
    CAMERA_FILTER_STATE_PAUSED,
    CAMERA_FILTER_STATE_RUNNING,
};

enum CAMERA_STEP_STATE
{
    CAMERA_STEP_STATE_START = 0,
    CAMERA_STEP_STATE_CONNECT_AFILTER = 1,
    CAMERA_STEP_STATE_CONNECT_VFILTER = 2,
    CAMERA_STEP_STATE_OPEN_DEVICE = 3,
    CAMERA_STEP_STATE_UNPACK_JSON = 4,
    CAMERA_STEP_STATE_JSON = 5,
    CAMERA_STEP_STATE_CONTEXT = 6,
};

struct CAMERA_CREATE_STEP_STATE
{
    CAMERA_STEP_STATE step = CAMERA_STEP_STATE_START;
    INT32             error = 0;
};

struct VIDEO_PROC_AMP
{
    VIDEO_PROCAMP_TYPE type = VIDEO_PROCAMP_NONE;
    CAPTURE_ATTR       attr{};

    std::string toString() const
    {
        std::stringstream ss;
        ss << "type=" << type << ",capture attr:" << attr.toString();
        return ss.str();
    }
};

struct CAMERA_CONTROL
{
    CAMERA_CONTROL_TYPE type = CAMERA_CONTROL_NONE;
    CAPTURE_ATTR        attr{};

    std::string toString() const
    {
        std::stringstream ss;
        ss << "type=" << type << ",capture attr:" << attr.toString();
        return ss.str();
    }
};

struct CameraControlEvent
{
    std::string layerID = "";
    CAMERA_CONTROL_TYPE type = CAMERA_CONTROL_NONE;
    bool                state = true;
};

struct VIDEO_CAPTURE_FORMAT
{
    VIDEO_PIXEL_FORMAT format = PIXEL_FORMAT_UNKNOWN;
    float              width = .0f;
    float              height = .0f;
    float              rate = .0f;
    float              minRate = .0f;
    float              maxRate = .0f;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "format=" << format << ",width=" << width << ",height=" << height << ",rate=" << rate << ",minRate=" << minRate << ",maxRate=" << maxRate;
        return ss.str();
    }
};

enum CREATE_SOURCE_FAILED_REASON
{
    CREATE_NOT_FAIL = 0,
    CREATE_INIT_FAIL,
    CREATE_ALLOCATE_FAIL,
    CREATE_EFFECT_NOT_SUPPORT_FAIL,
    CREATE_START_FAIL,
    CREATE_VISUAL_NOT_FIND,
    CREATE_MODEL_NOT_FIND,
    CREATE_PENDING,
};

struct CAMERA_STATE
{
    INT32                       errorCode = 0;
    CREATE_SOURCE_FAILED_REASON reason = CREATE_NOT_FAIL;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "errorCode=" << errorCode << ",reason=" << reason;
        return ss.str();
    }
};

struct VISUAL_SOURCE_RESULT
{
    UINT32                      errorCode = 0;
    CAMERA_CREATE_STEP_STATE    createStepState{};
    CREATE_SOURCE_FAILED_REASON reason = CREATE_NOT_FAIL;
    VISUAL_TYPE                 type = VISUAL_NONE;
};

struct DETECT_COLOR_RANGE
{
    UINT32      interval = 5;
    UINT32      limitedCnt = 5;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "interval=" << interval << ",limitedCnt=" << limitedCnt;
        return ss.str();
    }
};

enum ANALOG_RENDER_TYPE
{
    ANALOG_RENDER_NONE,
    ANALOG_RENDER_WAVEOUT,
    ANALOG_RENDER_DSOUND,
};

struct AUDIO_CAPTURE_FORMAT
{
    ANALOG_RENDER_TYPE renderType = ANALOG_RENDER_NONE;
    UINT32             channels = 0;
    UINT32             sampleRate = 0;
    UINT32             bitsPerSample = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "renderType=" << renderType << ",channels=" << channels << ",sampleRate=" << sampleRate << ",bitsPerSample=" << bitsPerSample;
        return ss.str();
    }
};

struct CAMERA_SOURCE
{
    std::vector<VIDEO_PROC_AMP> procAmps{};
    std::vector<CAMERA_CONTROL> controls{};
    VIDEO_CAPTURE_FORMAT        captureFormat{};
    DSHOW                       dshow{};
    HWND                        hwnd = NULL;
    UINT32                      version = 0;
    UINT32                      timeOut = 0;
    COLOR_CONFIG                colorConfig{};
    CAMERA_STATE                state{};
    std::string                 placeholder = "";
    TRANSFORM                   prepareTrans{};
    CAMERA_FILTER_STATE         filterState = CAMERA_FILTER_STATE_STOPPED;
    DETECT_COLOR_RANGE          detectColorRange{};
    float                       maxRate = 60.0f;
    UINT32                      audioTrack = 0;
    DSHOW                       audioDevice{};
    AUDIO_CAPTURE_FORMAT        audioCapFormat{};
    AUDIO_SETTING               audioSetting{};

    std::string toString() const
    {
        std::stringstream ss;
        ss << "procAmp:";
        for (const auto amp : procAmps)
            ss << amp.toString() << ",";
        ss << "control:";
        for (const auto crl : controls)
            ss << crl.toString() << ",";
        ss << "dshow:" << dshow.toString() << ",hwnd=" << hwnd << ",version=" << version << ",timeOut=" << timeOut << ",captureFormat:" << captureFormat.toString() << ",colorConfig:" << colorConfig.toString() << ",state:" << state.toString() << ",path=" << placeholder << ",prepareTrans:" << prepareTrans.toString() << ",filterState=" << filterState << ",detectColorRange:" << detectColorRange.toString() << ",maxRate=" << maxRate;
        return ss.str();
    }
};

struct ANALOG_SOURCE
{
    VIDEO_CAPTURE_FORMAT videoCapFormat{};
    DSHOW                videoDevice{};
    bool                 openCuda = false;
    COLOR_CONFIG         colorConfig{};
    UINT32               audioTrack = 0;
    DSHOW                audioDevice{};
    AUDIO_CAPTURE_FORMAT audioCapFormat{};
    AUDIO_SETTING        audioSetting{};

    std::string toString() const
    {
        std::stringstream ss;
        ss << "videoCapFormat:" << videoCapFormat.toString() << ",videoDevice:" << videoDevice.toString() << ",colorConfig:" << colorConfig.toString() << ",openCuda=" << openCuda << ",audioTrack=" << audioTrack << ",audioDevice:" << audioDevice.toString() << ",audioCapFormat:" << audioCapFormat.toString() << ",audioSetting:" << audioSetting.toString();
        return ss.str();
    }
};

enum OBJECT_FIT_MODE
{
    OBJECT_FIT_MODE_CONTAIN = 0,
    OBJECT_FIT_MODE_COVER,
};

struct VIRTUAL_CAMERA_SOURCE
{
    std::string     refID = "";
    Gdiplus::SizeF  outputSize{};
    INT32           bkColor = 0;
    OBJECT_FIT_MODE fitMode = OBJECT_FIT_MODE_CONTAIN;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "refID=" << refID << ",outputSize=[" << outputSize.Width << "," << outputSize.Height << "],bkColor=" << bkColor << ",fitMode=" << fitMode;
        return ss.str();
    }
};

enum CASTMATE_OPTION_TYPE
{
    CASTMATE_OPTION_TYPE_LATENCY = 0,
    CASTMATE_OPTION_TYPE_ENABLE_RANDOM_PORT,
    CASTMATE_OPTION_TYPE_SET_AUDIO_SOURCE,
    CASTMATE_OPTION_TYPE_RESET_HOST,
    CASTMATE_OPTION_TYPE_ENABLE_SERVER,
    CASTMATE_OPTION_TYPE_SET_DEVICE_INFO,
    CASTMATE_OPTION_TYPE_SET_FRAME_STUCK,
    CASTMATE_OPTION_TYPE_SET_PERFORMANCE_CALLBACK,
    CASTMATE_OPTION_TYPE_VIDEO_OUTPUT_TYPE,
    CASTMATE_OPTION_TYPE_AUDIO_OUTPUT_TYPE,
    CASTMATE_OPTION_TYPE_ENABLE_NEW_MUXD,
    CASTMATE_OPTION_TYPE_ENABLE_NEW_ANDROID_WIREDCAST,
    CASTMATE_OPTION_TYPE_ENABLE_FAST_MIRROR,
    CASTMATE_OPTION_TYPE_SET_DECODE_MODE,
    CASTMATE_OPTION_TYPE_SET_PERFORMANCE_EVENT,
    CASTMATE_OPTION_TYPE_SET_AB_CONFIG,
    CASTMATE_OPTION_TYPE_SET_VOUT_MODE,
};

struct BYTELINK_OPTION
{
    CASTMATE_OPTION_TYPE castmateOptionType = CASTMATE_OPTION_TYPE_LATENCY;
    std::string          config = "";

    std::string toString() const
    {
        std::stringstream ss;
        ss << "castmateOptionType=" << castmateOptionType << ",config=" << config;
        return ss.str();
    }
};

enum CASTMATE_PROTOCOL_TYPE
{
    WIRELESS_ANDROID = 0, // android 手机投屏协议
    WIRELESS_IOS,         // ios 手机、mac 电脑投屏协议
    WIRED_ANDROID,        // 有线版
    WIRED_IOS,            // 有线版
    WIRELESS_CAMERA,
    WIRED_ANDROID_CAMERA,
    WIRED_IOS_CAMERA,
    NOT_REACHED,
};

struct BYTELINK_SOURCE
{
    std::vector<BYTELINK_OPTION> bytelinkOptions{};
    CASTMATE_PROTOCOL_TYPE       protocolType = WIRELESS_ANDROID;
    VIDEO_PIXEL_FORMAT           format = PIXEL_FORMAT_UNKNOWN;
    AV_BUFFER_TYPE               bufferType = AV_BUFFER_NONE;
    std::string                  name = "";
    std::string                  closePic = "";
    UINT32                       latency = 0;
    COLOR_CONFIG                 colorConfig{};
    UINT32                       audioTrack = 0;
    AUDIO_SETTING                audioSetting{};
    float                        fps = 30.0f;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "bytelinkOption:";
        for (const auto option : bytelinkOptions)
            ss << option.toString() << ",";
        ss << "protocolType=" << protocolType << ",format" << format << ",bufferType=" << bufferType << ",name=" << name << ",closePic=" << closePic << ",latency=" << latency << ",colorConfig:" << colorConfig.toString() << ",audioTrack=" << audioTrack << ",audioSetting:" << audioSetting.toString();
        return ss.str();
    }
};

enum GRAFFITI_TYPE
{
    GRAFFITI_NONE = 0,
    GRAFFITI_RECT,
    GRAFFITI_ARROW,
    GRAFFITI_ELLIPSE,
    GRAFFITI_CURVE,
    GRAFFITI_TEXT,
    GRAFFITI_LINE,
    GRAFFITI_ERASE,
    GRAFFITI_RECT_FILL,
    GRAFFITI_ELLIPSE_FILL,
};

struct COLOR_RGBA
{
    LONG R = 255;
    LONG G = 255;
    LONG B = 255;
    LONG A = 255;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "[" << R << "," << G << "," << B << "," << A << "]";
        return ss.str();
    }
};

struct GRAFFITI_SOURCE
{
    GRAFFITI_TYPE type = GRAFFITI_RECT;
    std::string   trackInfo = "";
    bool          editable = false;
    bool          hasTrackers = false;
    bool          hasRestores = false;
    float         penWidth = 1.0f;
    COLOR_RGBA    colorRGBA{};

    std::string toString() const
    {
        std::stringstream ss;
        ss << "type=" << type << ",trackInfo=" << trackInfo << ",editable=" << editable << ",hasTrackers=" << hasTrackers << ",hasRestores=" << hasRestores << ",penWidth=" << penWidth << ",colorRGBA:" << colorRGBA.toString();
        return ss.str();
    }
};

enum AUDIO_OBSERVER_TYPE
{
    AUDIO_OBSERVER_ALL = 0,
    AUDIO_OBSERVER_SINGLE_USER,
};

enum RTC_STREAM_TYPE
{
    UNKNOWN_STREAM = 0,
    VIDEO_STREAM,
    SCREEN_STREAM,
};

struct RTC_SOURCE
{
    RTC_STREAM_TYPE     streamType = UNKNOWN_STREAM;
    AUDIO_OBSERVER_TYPE observerType = AUDIO_OBSERVER_ALL;
    std::string         uid = "";
    bool                enableAudioInput = false;
    UINT32              audioTrack = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "streamType=" << streamType << ",observerType=" << observerType << ",uid=" << uid << ",enableAudioInput=" << enableAudioInput << ",audioTrack=" << audioTrack;
        return ss.str();
    }
};

enum RTC_AUDIO_SOURCE_TYPE
{
    RTC_AUDIO_SOURCE_TYPE_EXTERNAL = 0,
    RTC_AUDIO_SOURCE_TYPE_INTERNAL = 1,
};

enum MOVE_ORDER
{
    MOVE_NONE = 0, // layer not move
    MOVE_UP,       // up one layer
    MOVE_DOWN,     // down one layer
    MOVE_TOP,      // top layer
    MOVE_BOTTOM,   // bottom layer
};

enum LAYER_LAYOUT
{
    LAYOUT_NONE = 0,
    LAYOUT_CONTAIN_RATIO_FILL,
    LAYOUT_TILE_FILL,
    LAYOUT_CENTERED,
    LAYOUT_COVER_RATIO_FILL,
    LAYOUT_HORIZONTAL_CENTERED,
    LAYOUT_VERTICAL_CENTERED,
};

enum LAYER_FIXED_EDGE
{
    FIXED_EDGE_NONE = 0,
    FIXED_EDGE_WIDTH,
    FIXED_EDGE_HEIGHT,
    FIXED_EDGE_LONGEST,
    FIXED_EDGE_SHORTEST,
};

struct SOURCE_INFO
{
    // public attributes
    VISUAL_TYPE          type = VISUAL_NONE;
    UINT64               id = 0;
    float                fps = 60.0f;
    Gdiplus::SizeF       size{.0f, .0f};
    bool                 isCreated = false;
    bool                 destroyWhenAllRefRemoved = true;
    bool                 isCloned = false;
    VISUAL_SOURCE_RESULT result{};
    std::vector<UINT64>  layerIDs{};

    // extra attributes
    std::variant<WINDOW_SOURCE,
        GAME_SOURCE,
        MONITOR_SOURCE,
        FAV_SOURCE,
        IMAGE_SOURCE,
        CAMERA_SOURCE,
        ANALOG_SOURCE,
        BROWSER_SOURCE,
        BYTELINK_SOURCE,
        GRAFFITI_SOURCE,
        RTC_SOURCE,
        VIRTUAL_CAMERA_SOURCE>
        source{};
};

struct COMPOSITE_SOURCE_META 
{
	VISUAL_TYPE primaryType = VISUAL_NONE;
	UINT64 primarySourceID = 0;
	UINT64 fallbackSourceID = 0;
    bool isFallback = false;
    std::variant<CAMERA_SOURCE, GAME_SOURCE> source{};
};

struct LAYER_INFO
{
    UINT64                   canvasID = 0;
    UINT64                   id = 0;
    UINT64                   sourceID = 0;
    bool                     show = true;
    bool                     locked = false;
    bool                     isReady = false;
    bool                     isCreated = false;
    bool                     createNotCurrentCanvas = false;
    TRANSFORM                transform{};
    CLIPF                    prepareClip{};
    LAYER_LAYOUT             layout = LAYOUT_NONE;
    LAYER_FIXED_EDGE         fixedEdge = FIXED_EDGE_NONE;
    CLIPF                    moveRange = {.0f, .0f, .0f, .0f};
    Gdiplus::SizeF           targetSize = {.0f, .0f};
    CLIPF                    refLayout = {.0f, .0f, .0f, .0f};
    std::vector<FILTER>      filters{};
    std::vector<VISUAL_FLAG> visualFlags{};
};

// mode scene canvas layer source
struct CANVAS_INFO_EX : public CANVAS_INFO
{
    std::vector<UINT64>     order;
    std::vector<LAYER_INFO> layers{};
};

struct SCENE_INFO_EX : public SCENE_INFO
{
    std::vector<CANVAS_INFO_EX> canvas{};
};

struct MODE_INFO_EX : public MODE_INFO
{
    std::vector<SCENE_INFO_EX> scenes{};
    UINT64                     currentSceneID = 0;
};

enum VISUAL_PREVIEW_FILL
{
    PREVIEW_RATIO_FILL = 0,
    PREVIEW_TILE_FILL = 1,
    PREVIEW_CLIP_FILL = 2,
};

struct LAYER_PREVIEW
{
    Gdiplus::SizeF      size{};
    TRANSFORM           transform{};
    INT32               bkColor = 0;
    VISUAL_PREVIEW_FILL fillType = PREVIEW_RATIO_FILL;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "size=[" << size.Width << "," << size.Height << "],transform:" << transform.toString() << ",bkColor=" << bkColor << ",fillType=" << fillType;
        return ss.str();
    }
};

struct PREVIEW_PARAMS
{
    bool  isPopUp =  false;
    float topBorderRadius = .0f;
    float bottomBorderRadius = .0f;
    float opacity = .0f;
};

struct VIBE_LAYERS_ZORDER
{
    std::string              actionID = "";
    std::vector<std::string> layerIDs;
};

struct VIBE_LAYER_TRANSFORM
{
    std::string                       actionID = "";
    std::string                       layerID = "";
    UINT64                            durationMs = 800;
    TRANSITION_PROGRESS_FUNCTION_TYPE transitionProgressFuncType = TRANSITION_PROGRESS_FUNCTION_LINEAR;
    TRANSFORM                         targetTransform{};
};

struct VIBE_LAYER_VISIBLE
{
    std::string                       actionID = "";
    std::string                       layerID = "";
    bool                              visible = false;
    UINT64                            durationMs = 500;
    TRANSITION_PROGRESS_FUNCTION_TYPE transitionProgressFuncType = TRANSITION_PROGRESS_FUNCTION_LINEAR;
};

struct VIBE_LAYER_PREPROCESS
{
    std::string actionID = "";
    std::string layerID = "";
    bool        needPreprocess = false;
};

struct VIBE_PREPROCESS_INFO
{
    std::vector<VIBE_LAYER_PREPROCESS> layersPreprocess{};
};

enum VIBE_ACTION_TYPE
{
    VIBE_ACTION_TYPE_NONE = 0,
    VIBE_ACTION_TYPE_CREATE,
    VIBE_ACTION_TYPE_DESTROY,
    VIBE_ACTION_TYPE_UPDATE,
    VIBE_ACTION_TYPE_ACTIVE,
    VIBE_ACTION_TYPE_PLAY,
    VIBE_ACTION_TYPE_STOP,
};

struct VIBE_FILTER_INFO
{
    std::string      actionID = "";
    VIBE_ACTION_TYPE actionType = VIBE_ACTION_TYPE_NONE;
    FILTER           filter{};
};

struct VIBE_AUDIO_AMBIENT
{
    std::string      actionID = "";
    VIBE_ACTION_TYPE actionType = VIBE_ACTION_TYPE_NONE;
    std::string      audioID = "";
    PCM_AUDIO        audioData{};
    AUDIO_CAPTURE    audioCapture{};
};

struct VIBE_TRIGGER_EFFECT
{
    std::string                       canvasID = "";
    VIBE_LAYERS_ZORDER                layersZorder{};
    std::vector<VIBE_LAYER_TRANSFORM> layersTransform{};
    std::vector<VIBE_LAYER_VISIBLE>   layersVisible{};
    std::vector<VIBE_FILTER_INFO>     filterInfos{};
    VIBE_PREPROCESS_INFO              preprocessInfo{};
    VIBE_AUDIO_AMBIENT                audioAmbient{};
};

enum IMAGE_FORMAT
{
    IMAGE_FORMAT_BMP = 0,
    IMAGE_FORMAT_JPG,
    IMAGE_FORMAT_PNG,
    IMAGE_FORMAT_TIFF,
    IMAGE_FORMAT_GIF,
    IMAGE_FORMAT_WMP,
};

struct THUMBNAIL_INFO_CACHE
{
    LIVE_MODE    mode = LIVE_MODE_MAX;
    UINT64       sceneID = 0;
    UINT32       videoModel = 0;
    std::string  path = "";
    IMAGE_FORMAT format = IMAGE_FORMAT_PNG;
    void*        cbparam = nullptr;
};

enum ABRSTRATEGY_TYPE
{
    ABR_NONE = 0,
    ABR_COMMON,
    ABR_SENSITIVE,
    ABR_MORESENS,
};

struct ABR_STRATEGY
{
    ABRSTRATEGY_TYPE strategyType = ABR_NONE;
    UINT32           minBitrate = 0;
    UINT32           offset = 0;
    std::string toString() const
    {
        std::stringstream ss;
        ss << "strategy=" << strategyType << ",minBitrate=" << minBitrate << ",offset = " << offset;
        return ss.str();
    }
};

enum STREAM_REASON_TYPE
{
    START_STREAM = 0,
    START_RECORD,
    STOP_NORMAL,
    STOP_RTCMIX,
    STOP_FORBIDDEN,
    STOP_OUT_OF_TIME,
    STOP_CODE_ERROR,
    STOP_FATAL_ERROR,
};

struct STOP_STREAM_PARAM 
{
    std::string        streamID = "";
    STREAM_REASON_TYPE reason = STOP_NORMAL;
};

enum BUFFER_TYPE
{
    BUFFER_TYPE_UNKNOWN = 0,
    BUFFER_TYPE_MEMORY,
    BUFFER_TYPE_TEXTURE,
};

struct VIDEO_MIX_PARAM
{
    Gdiplus::SizeF     outputSize{ .0f, .0f };
    COLOR_CONFIG       colorConfig{};
    VIDEO_PIXEL_FORMAT format = PIXEL_FORMAT_UNKNOWN;
    BUFFER_TYPE        bufferType = BUFFER_TYPE_UNKNOWN;
    bool               focusNv12 = false;
    float              fps = 30.0f;
};

enum SEI_VALUE_TYPE
{
    SEI_UNKNOWN = 0,
    SEI_STRING,
    SEI_JSON,
    SEI_INTEGER,
    SEI_FLOAT,
};

enum SEI_OPT
{
    SEI_OPT_UNKNOWN = 0,
    SEI_OPT_UPDATE,
    SEI_OPT_REMOVE,
};

struct SEI_INFO
{
    UINT32         videoModel = 0;
    std::string    streamID = "";
    std::string    key = "";
    std::string    sei = "";
    SEI_VALUE_TYPE seiType = SEI_VALUE_TYPE::SEI_UNKNOWN;
    SEI_OPT        seiOpt = SEI_OPT_UNKNOWN;
    bool           flushImmediately = false;
};

struct VIDEO_ENCODER_INFO
{
    std::string name = "";
    std::string codec = ""; // h264, h265
    bool        hardware = false;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "name=" << name << ",codec=" << codec << ",hardware=" << hardware;
        return ss.str();
    }
};

struct VIDEO_CODEC_PARAM
{
    std::string codecName = "";
    UINT32      bitrate = 0;
    std::string codec_param_json;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "codecID=" << codecName << ",bitrate=" << bitrate << ",codec_param_json=" << codec_param_json;
        return ss.str();
    }
};

enum PROFILE_AAC
{
    PROFILE_AAC_NONE = 0,
    PROFILE_AAC_MAIN,
    PROFILE_AAC_LOW,
    PROFILE_AAC_SSR,
    PROFILE_AAC_LTP,
    PROFILE_AAC_HE,
    PROFILE_AAC_HE_V2 = 29,
    PROFILE_AAC_LD = 23,
    PROFILE_AAC_ELD = 39,
};

struct AUDIO_CODEC_PARAM
{
    std::string                codecName = "FFmpegAACAudioEncoderSource";
    UINT32                     bitrate = 192;
    std::optional<PROFILE_AAC> aacProfile;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "codecID=" << codecName << ",bitrate=" << bitrate;
        return ss.str();
    }
};

enum MEDIA_STREAM_TYPE
{
    MEDIA_STREAM_NULL = 0,
    MEDIA_STREAM_AUDIO,
    MEDIA_STREAM_VIDEO,
    MEDIA_STREAM_BOTH,
};

enum AUDIO_PROPERTIES_MODE
{
    AUDIO_PROPERTIES_MODE_MICROPHONE = 0,
    AUDIO_PROPERTIES_MODE_AUDIO_MIXING,
};

enum RTMP_UPLOAD_SPEED_TEST_RESULT
{
    RTMP_UPLOAD_SPEED_TEST_RESULT_DEFAULT = 0,
    RTMP_UPLOAD_SPEED_TEST_RESULT_SUCCEED,
    RTMP_UPLOAD_SPEED_TEST_RESULT_FAILED,
};

enum RTMP_UPLOAD_SPEED_TEST_FAIL_REASON
{
    RTMP_UPLOAD_SPEED_TEST_NO_FAIL = 0,
    RTMP_UPLOAD_SPEED_TEST_NOT_CONNECTED,
    RTMP_UPLOAD_SPEED_TEST_PUSH_STREAM_FAIL,
    RTMP_UPLOAD_SPEED_TEST_PACKET_SEND_TIME_TOO_SHORT,
    RTMP_UPLOAD_SPEED_TEST_EMPTY_URL,
    RTMP_UPLOAD_SPEED_TEST_UNSUPPORTED_URL,
    RTMP_UPLOAD_SPEED_TEST_CANNOT_START_WHEN_NOT_ENDED,
    RTMP_UPLOAD_SPEED_TEST_CANNOT_STOP_WHEN_NOT_STARTED,
};

struct RTMP_CONNECT_START_INFO
{
    std::string streamID = "";
    std::string version = "";
    std::string projectKey = "";
    UINT32      errorCode = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "streamID=" << streamID << ",version=" << version << ",projectKey=" << projectKey << ",errorCode=" << errorCode;
        return ss.str();
    }
};

struct RTMP_CONNECT_END_INFO
{
    std::string streamID = "";
    std::string version = "";
    std::string projectKey = "";
    UINT32      errorCode = 0;
    INT32       state = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "streamID=" << streamID << ",version=" << version << ",projectKey=" << projectKey << ",errorCode=" << errorCode << ",state=" << state;
        return ss.str();
    }
};

struct RTMP_RECONNECT_INFO
{
    std::string streamID = "";
    std::string version = "";
    std::string projectKey = "";
    std::string message = "";
    UINT32      errorCode = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "streamID=" << streamID << ",version=" << version << ",projectKey=" << projectKey << ",message=" << message << ",errorCode=" << errorCode;
        return ss.str();
    }
};

struct RTMP_CONNECT_RESULT_INFO
{
    std::string streamID = "";
    INT32       connectSuccess = 0;
    INT32       sceneCode = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "streamID=" << streamID << ",connectSuccess=" << connectSuccess << ",sceneCode=" << sceneCode;
        return ss.str();
    }
};

struct RTMP_PUSH_STREAM_END_INFO
{
    std::string streamID = "";
    std::string version = "";
    std::string projectKey = "";
    UINT32      interval = 0;
    UINT32      inputTrack = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "streamID=" << streamID << ",version=" << version << ",projectKey=" << projectKey << ",interval=" << interval << ",inputTrack=" << inputTrack;
        return ss.str();
    }
};

enum STREAM_TYPE
{
    STREAM_NONE = 0,
    STREAM_LIVE_RTMP,
    STREAM_RECORD_FFMPEG,
    STREAM_RECORD_FLV,
    STREAM_LIVE_FFMPEG,
    STREAM_LIVE_RTS,
    STREAM_LIVE_BYTE_RTMP,
    STREAM_LIVE_BYTE_RTMPQ,
    STREAM_LIVE_BYTE_RTMPS,
    STREAM_RTC,
    STREAM_NDI,
    STREAM_VIRTUAL_CAMERA,
};

struct RTMP_BW_PROBE_INFO
{
    std::string                        streamID = "";
    std::string                        version = "";
    std::string                        projectKey = "";
    float                              averageTransportBitrate = .0f;
    float                              probRtt = .0f;
    float                              probBandwidth = .0f;
    UINT32                             totalSends = 0;
    UINT32                             totalDrops = 0;
    UINT32                             totalDuration = 0;
    UINT32                             totalSendDuration = 0;
    STREAM_TYPE                        streamType = STREAM_NONE;
    RTMP_UPLOAD_SPEED_TEST_RESULT      result{};
    RTMP_UPLOAD_SPEED_TEST_FAIL_REASON failedReason{};

    std::string toString() const
    {
        std::stringstream ss;
        ss << "streamID=" << streamID << ",version=" << version << ",projectKey=" << projectKey << ",averageTransportBitrate=" << averageTransportBitrate << ",probRtt=" << probRtt << ",probBandwidth=" << probBandwidth << ",totalSends=" << totalSends << ",totalDrops=" << totalDrops << ",totalDuration=" << totalDuration << ",totalSendDuration=" << totalSendDuration << ",streamType=" << streamType << ",result=" << result << ",failedReason=" << failedReason;
        return ss.str();
    }
};

struct STREAM_INFO
{
    STREAM_TYPE type = STREAM_NONE;
    std::string url = "";
    std::string key = "";
    UINT32      reconnectCnt = 0;
    UINT32      reconnectTime = 0;

    // optional params
    std::optional<UINT32>      delay;
    std::optional<std::string> cipher;
    std::optional<std::string> plainText;
    std::optional<std::string> vpaasConfig;
    std::optional<std::string> customMetadata;
    std::optional<std::string> cdnIps;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "type=" << type << ",reconnectCnt=" << reconnectCnt << ",reconnectTime=" << reconnectTime;
        return ss.str();
    }
};

struct OUTPUT_INFO
{
    UINT32                     videoModel = 0;
    UINT32                     audioTrack = 0;
    std::string                streamID = "";
    std::string                codecID = "VIDEO_CODEC_ID_0";
    Gdiplus::SizeF             outputSize{ .0f, .0f };
    VIDEO_CODEC_PARAM          videoCodec{};
    AUDIO_CODEC_PARAM          audioCodec{};
    ABR_STRATEGY               abrStrategy{};
    STREAM_INFO                streamInfo{};
    std::optional<STREAM_INFO> fallbackStreamInfo;
    std::string                flvMetadataOrientation = "";
    STREAM_REASON_TYPE         reason = START_STREAM;
    std::string toString() const
    {
        std::stringstream ss;
        ss << "videoModel=" << videoModel << ",audioTrack=" << audioTrack << ",streamID=" << streamID << ",codecID=" << codecID << ",outputSize=[" << outputSize.Width << "," << outputSize.Height << "],videoCodec:" << videoCodec.toString() << ",audioCodec:" << audioCodec.toString() << ",strategy:" << abrStrategy.toString() << ",streamInfo:" << streamInfo.toString();
        return ss.str();
    }
};

struct ROOM_INFO
{
    std::string token = "";
    std::string roomID = "";

    std::string toString() const
    {
        std::stringstream ss;
        ss << "token=" << token << ",roomID=" << roomID;
        return ss.str();
    }
};

struct CLIP_AREA_INFO
{
    Gdiplus::RectF clip = { .0f, .0f, .0f, .0f };
    float          scale = .0f;
    float          outputScale = .0f;
    UINT32         fps = 0;
    UINT32         maxBitrate = 0;
    UINT32         minBitrate = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "clip=[" << clip.X << "," << clip.Y << "," << clip.Width << "," << clip.Height << "],scale=" << scale << ",outputScale=" << outputScale << ",fps=" << fps << ",maxBitrate=" << maxBitrate << ",minBitrate=" << minBitrate;
        return ss.str();
    }
};

struct RTC_LINK
{
    UINT32         videoModel = 0;
    std::string    host = "";
    std::string    deviceID = "";
    std::string    appID = "";
    std::string    userID = "";
    std::string    tncParams = "";
    std::string    businessID = "";
    ROOM_INFO      roomInfo{};
    CLIP_AREA_INFO clipAreaInfo{}; // video clip output region
    bool           autoPublish = false;
    bool           enableExternalAudio = false;
    bool           enableAudioProcessor = false;
    UINT32         audioTrack = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "videoModel=" << videoModel << ",host=" << host << ",deviceID=" << deviceID << ",appID=" << appID << ",userID=" << userID << ",tncParams=" << tncParams << ",businessID=" << businessID << ",roomInfo:" << roomInfo.toString() << ",clipAreaInfo:" << clipAreaInfo.toString() << ",autoPublish=" << autoPublish << ",enableExternalAudio=" << enableExternalAudio << ",enableAudioProcessor=" << enableAudioProcessor << ",audioTrack=" << audioTrack;
        return ss.str();
    }
};

struct ACTIVE_STATISTIC
{
    std::string codecName = "";
    double      videoEncFps = .0;  // encoder fps
    double      videoSendFps = .0; // actually send fps, affect by network
    double      videoEncErrorFps = 0;
    double      sendBitrate = .0;
    double      videoSendBitrate = .0;
    double      audioSendBitrate = .0;
    double      videoEncBitrate = .0;
    double      audioEncBitrate = .0;
    INT64       effectElapse = 0;
    INT64       encodeElapse = 0;
    INT64       renderElapse = 0;
    UINT64      totalDrops = 0;
    UINT64      totalVideoPackets = 0;
    
    UINT64      videoEncInPts = 0; // pts, dts in microsecond
    UINT64      videoEncOutPts = 0;
    UINT64      videoEncOutDts = 0;
    UINT64      audioEncInPts = 0;
    UINT64      audioEncOutPts = 0;
    UINT64      audioEncOutDts = 0;
    UINT64      packageDelayMs = 0; // package delay
};

struct PERFORMANCE_MATRICS
{
    std::string name = "";
    UINT32      threadshold = 0; // ms
};

enum LS_PERFORMANCE_FLAG
{
    PERFORMANCE_FLAG_GPU = 1 << 1,
    PERFORMANCE_FLAG_SYSMEM = 1 << 2,
    PERFORMANCE_FLAG_RENDERFPS = 1 << 3,
    PERFORMANCE_FLAG_ENCODEFPS = 1 << 4,
    PERFORMANCE_FLAG_UPLOADBITRATE = 1 << 5,
    PERFORMANCE_FLAG_LOSSRATE = 1 << 6,
};

struct LS_PERFORMANCE_INFO
{
    UINT64 flags = 0;
    float  gpu = .0f;
    float  mem = .0f;
    float  renderFps = .0f;
    float  encodeFps = .0f;
    UINT64 uploadBitrate = 0;
    UINT64 lossRate = 0;
};

struct VIDEO_ADAPTER_INFO
{
    std::string adapterModel = "";
    std::string pciId = "";
    std::string adapterLuid = "";
    std::string gpuDriverVersion = "";
    INT64       dedicatedMemorySize = 0;
    INT64       sharedMemorySize = 0;
    INT64       gpu3DTotal = 0;
    INT64       gpu3DUsage = 0;
    INT64       gpuDedicatedMemoryTotal = 0;
    INT64       gpuDedicatedMemoryUsage = 0;
    // bool        isEncodeActive = false;
    UINT vendorId = 0;
    LUID        adapterLuidValue;
};

struct CPU_INFO
{
    std::wstring processorName = L"";
    UINT32       maxClockSpeed = 0;
    UINT32       processNum = 0;
    UINT32       coreNum = 0;
    INT32        family = 0;
    INT32        model = 0;
    INT32        stepping = 0;
};

struct MONITOR_INFO
{
    UINT32   gpuIndex = 0;
    UINT32   left = 0;
    UINT32   top = 0;
    UINT32   right = 0;
    UINT32   bottom = 0;
    UINT32   refresh = 0;
    HMONITOR monitor{};
    bool     isHDR = false;
    UINT32   physWidth = 0;
    UINT32   physHeight = 0;
};

struct ADAPTER_INFO
{
    std::wstring adapterName = L"";
    UINT32       vendorID = 0;
    UINT32       deviceID = 0;

    std::string toString() const
    {
        std::stringstream ss;
        ss << "adapterName=" << adapterName.c_str() << ",vendorID=" << vendorID << ",deviceID=" << deviceID;
        return ss.str();
    }
};

enum AUDIO_REPORT_MODE
{
    AUDIO_REPORT_MODE_NORMAL = 0,
    AUDIO_REPORT_MODE_DISCONNECT,
    AUDIO_REPORT_MODE_RESET,
};

struct AUDIO_PROPERTIES_REPORT
{
    UINT32                interval = 0;
    float                 smooth = .0f;
    bool                  enableSpectrum = false;
    bool                  enableVad = false;
    bool                  enableVoicePitch = false;
    AUDIO_REPORT_MODE     reportMode{};
    AUDIO_PROPERTIES_MODE audioReportMode{};
};

struct AUDIO_PERFORMANCE_INFO
{
    bool  valid = false;
    INT32 resetTimes = 0;
    INT32 cost = 0;
    INT32 offset = 0;
};

struct STORE_INFO
{
    SCENE_INFO                lastSceneInfo{};
    std::vector<MODE_INFO_EX> modeInfos{};
};

struct InitEvent
{
    bool   success = false;
    INT32  dxError = 0;
    UINT32 pid = 0;
};

struct CreateVisualEvent
{
    VISUAL_TYPE                 type = VISUAL_NONE;
    std::string                 visualID = "";
    INT32                       errCode = 0;
    bool                        success = false;
    CREATE_SOURCE_FAILED_REASON reason{};
};

struct FallbackVisualEvent
{
    VISUAL_TYPE                 type = VISUAL_NONE;
    std::string                 visualID = "";
    INT32                       errCode = 0;
    bool                        success = false;
    CREATE_SOURCE_FAILED_REASON reason{};
    bool                        fallbackToPlaceHolder = false;
};

struct DeleteVisualEvent
{
    std::string visualID = "";
    bool        success = false;
};

struct SelectVisualEvent
{
    std::string visualID = "";
    UINT32      videoModel = 0;
    bool        manual = false;
};

struct TransformChangedEvent
{
    std::string    visualID = "";
    TRANSFORM      transform{};
    Gdiplus::SizeF canvasSize = { .0f, .0f };
    Gdiplus::SizeF visualSize = { .0f, .0f };
    LAYER_LAYOUT   layout = LAYOUT_NONE;
};

struct VibeTransformAnimationFinishedEvent
{
    std::string visualID = "";
    std::string actrionID = "";
};

struct VibeVisibleAnimationFinishedEvent
{
    std::string visualID = "";
    std::string actionID = "";
};

struct SizeScaleChangedEvent
{
    std::string    visualID = "";
    Gdiplus::SizeF size = { .0f, .0f };
};

struct OutputThumbnailEvent
{
    void* functor;
    bool  success = false;
};

enum CASTMATE_EVENT_TYPE
{
    CASTMATE_EVENT_CLOSE = 0,
    CASTMATE_EVENT_FIRSTFRAME,
    CASTMATE_EVENT_CONNECTED,
    CASTMATE_EVENT_DISCONNECTED,
    CASTMATE_EVENT_PLUGIN,
    CASTMATE_EVENT_PLUGOUT,
    CASTMATE_EVENT_CASTMATE_EVENT,
    CASTMATE_EVENT_ONERROR,
    CASTMATE_EVENT_ONSDKERROR,
};

struct CreateCastMateEvent
{
    CASTMATE_EVENT_TYPE    eventType = CASTMATE_EVENT_CLOSE;
    CASTMATE_PROTOCOL_TYPE protocolType = WIRELESS_ANDROID;
    UINT64                 eventCode = 0;
    std::string            msg = "";
};

enum GAME_CAPTURE_EVENT_TYPE
{
    CAPTURE_SUCCESS_MEM = 0,
    CAPTURE_SUCCESS_TEXTURE,
    CAPTURE_ERROR_TIME_OUT,
    CAPTURE_ERROR_TEXTURE,
    CAPTURE_ERROR_INITIALIZE,
    CAPTURE_ERROR_INTERNAL,
    CAPTURE_ERROR_HOOK,
    CAPTURE_ERROR_DEVICE,
    CAPTURE_EVENT_COMMON,
    CAPTURE_EVENT_INJECT,
    CAPTURE_EVENT_FIRSTFRAME,
    CAPTURE_EVENT_DX9CPU_HOOK,
    CAPTURE_EVENT_DX9GPU_HOOK,
    CAPTURE_EVENT_DX10CPU_HOOK,
    CAPTURE_EVENT_DX10GPU_HOOK,
    CAPTURE_EVENT_DX101CPU_HOOK,
    CAPTURE_EVENT_DX101GPU_HOOK,
    CAPTURE_EVENT_DX11CPU_HOOK,
    CAPTURE_EVENT_DX11GPU_HOOK,
    CAPTURE_EVENT_DX12CPU_HOOK,
    CAPTURE_EVENT_DX12GPU_HOOK,
    CAPTURE_EVENT_GLCPU_HOOK,
    CAPTURE_EVENT_GLGPU_HOOK,
    CAPTURE_EVENT_VULKANCPU_HOOK,
    CAPTURE_EVENT_VULKANGPU_HOOK,
    CAPTURE_EVENT_STOP,
    CAPTURE_ERROR_MEM_CREATE_ERROR,
    CAPTURE_EVENT_VULKANCPU_HOOK_FAIL = 50,
    CAPTURE_EVENT_VULKANGPU_HOOK_FAIL,
    CAPTURE_EVENT_VULKANALL_HOOK_FAIL,
};

struct GameStateEvent
{
    std::string             layerID = "";
    std::string             sourceID = "";
    std::string             info = "";
    GAME_CAPTURE_EVENT_TYPE type = CAPTURE_SUCCESS_MEM;
    UINT64                  timestamp = 0;
};

struct GAME_RETRY_CONTEXT
{
    UINT32 attemptCount = 0;
    UINT64 firstFailureTime = 0;
    std::set<UINT64> pendinLayers{};
};

enum FAV_EVENT_TYPE
{
    FAV_EVENT_UNKNOW = 0,
    FAV_EVENT_OPENED,
    FAV_EVENT_PAUSE_CHANGED,
    FAV_EVENT_POSITION_CHANGED,
    FAV_EVENT_READ_EOF,
    FAV_EVENT_ERROR,
    FAV_EVENT_FAIL_TO_OPEN,
    FAV_EVENT_ERROR_EOF,
    FAV_EVENT_PLAY_EOF,
    FAV_EVENT_READ_PACKET_TIMEOUT,
};

struct FavStateEvent
{
    std::string    visualID = "";
    bool           isAnimation = false;
    FAV_EVENT_TYPE type = FAV_EVENT_UNKNOW;
};

struct TracksInfoEvent
{
    std::string layerID = "";
    std::string tracksInfo = "";
};

enum CURSOR_HIT_POSITION
{
    HIT_POSITION_NOTHING = 0,
    HIT_POSITION_TOP_LEFT,
    HIT_POSITION_TOP_RIGHT,
    HIT_POSITION_BOTTOM_RIGHT,
    HIT_POSITION_BOTTOM_LEFT,
    HIT_POSITION_TOP,
    HIT_POSITION_RIGHT,
    HIT_POSITION_BOTTOM,
    HIT_POSITION_LEFT,
    HIT_POSITION_MIDDLE,
};

struct BeginTrackEvent
{
    std::string         layerID = "";
    CURSOR_HIT_POSITION hitPos = HIT_POSITION_NOTHING;
};

struct EndTrackEvent
{
    std::string         layerID = "";
    CURSOR_HIT_POSITION hitPos = HIT_POSITION_NOTHING;
};

enum MONITOR_THREAD_ID
{
    MONITOR_THREAD_ID_RENDER_THREAD = 0,
};

enum MONITOR_THREAD_EVENT_TYPE
{
    MONITOR_THREAD_EVENT_TYPE_START = 0,
    MONITOR_THREAD_EVENT_TYPE_TICK = 1,
    MONITOR_THREAD_EVENT_TYPE_END = 2,
};

struct ClipMaskEndEvent
{
    std::string layerID = "";
};

enum MOUSE_CLICK_EVENT_TYPE
{
    MOUSE_CLICK_EVENT_DOWN = 0,
    MOUSE_CLICK_EVENT_UP,
    MOUSE_CLICK_EVENT_VALID_AREA,
    MOUSE_CLICK_EVENT_INVALID_AREA,
};

struct MouseClickEvent
{
    std::string            visualID = "";
    UINT32                 videoModel = 0;
    MOUSE_CLICK_EVENT_TYPE type = MOUSE_CLICK_EVENT_DOWN;
};

struct MouseDoubleClickEvent
{
    std::string layerID = "";
    UINT32      videoModel = 0;
};

struct MouseEnterEvent
{
    UINT32 videoModel = 0;
};

struct MouseLeaveEvent
{
    UINT32 videoModel = 0;
};

struct MouseLeftButtonDownEvent
{
};

struct StateChangeEvent
{
    std::string layerID = "";
    std::string sourceID = "";
    bool        state = false;
};

struct HittestChangeEvent
{
    std::string visualID = "";
};

enum SHORT_CUT_ACTION
{
    ACTION_KEY_NONE = 0,
    ACTION_KEY_UP,
    ACTION_KEY_DOWN,
    ACTION_KEY_LEFT,
    ACTION_KEY_RIGHT,
    ACTION_KEY_ALT_CLIP,
    ACTION_KEY_SHIFT_STRETCH,
};

struct ShortcutActionEvent
{
    std::string      layerID = "";
    SHORT_CUT_ACTION action = ACTION_KEY_NONE;
};

struct ColorRangeDetectEvent
{
    std::string layerID = "";
    COLOR_RANGE result = COLOR_RANGE::COLOR_RANGE_UNSPECIFIED;
};

enum CAMERA_OPEN_ERROR_TYPE
{
    CAMERA_OPEN_NO_ERROR = 0,
    CAMERA_REOPEN_ERROR = 1,
    CAMERA_DEVICE_OCCUPIED_ERROR = 2,
    CAMERA_DEVICE_UNKNOW_ERROR = 3,
};

struct CameraOpenErrorEvent
{
    std::string            layerID = "";
    CAMERA_OPEN_ERROR_TYPE errorType = CAMERA_OPEN_NO_ERROR;
    INT32                  errorCode = 0;
};

struct MjpgDecEvent
{
    std::string visualID = "";
    std::string cameraName = "";
    std::string preDec = "";
    std::string currentDec = "";
};

struct VisualReadyEvent
{
    std::string    visualID = "";
    Gdiplus::SizeF size = { .0f, .0f };
};

enum VISUAL_CAPTURE_TYPE
{
    CAPTURE_TYPE_NONE = 0,
    CAPTURE_TYPE_DDA,
    CAPTURE_TYPE_WGC,
    CAPTURE_TYPE_BITBLT,
    CAPTURE_TYPE_GAME_INJECT_TEXTURE,
    CAPTURE_TYPE_GAME_INJECT_MEMORY,
};

struct VisualCaptureTypeChangeEvent
{
    std::string         layerID = "";
    VISUAL_CAPTURE_TYPE type = CAPTURE_TYPE_NONE;
    uint32_t            capPID = 0;
    uint64_t            capHwnd = NULL;
};

struct GameSourceProcessCrashDetectEvent
{
    std::string visualID = "";
    std::string exeName = "";
    UINT32      pid = 0;
    UINT32      exitCode = 0;
    bool        exited = false;
};

struct FullScreenDetectorEvent
{
    std::string detectorID = "";
    bool        foundTarget = false;
    UINT64      winID = 0;
    UINT32      pid = 0;
    std::string className = "";
    std::string exeName = "";
};

struct CreateFrameEvent
{
    std::string    frameID = "";
    Gdiplus::SizeF frameSize{};
    INT32          errCode = 0;
    std::string    errMsg = "";
};

enum AUDIO_DATA_FLOW
{
    AUDIO_DATA_FLOW_INPUT = 0,
    AUDIO_DATA_FLOW_OUTPUT = 1,
    AUDIO_DATA_FLOW_ALL = 2,
};

enum AUDIO_DEVICE_ROLE
{
    AUDIO_DEVICE_ROLE_CONSOLE = 0,
    AUDIO_DEVICE_ROLE_MULTI_MEDIA = 1,
    AUDIO_DEVICE_ROLE_COMMUNICATIONS = 2,
};

enum AUDIO_DEVICE_STATE
{
    AUDIO_DEVICE_STATE_ALL = 0,
    AUDIO_DEVICE_STATE_ACTIVE = 1,
    AUDIO_DEVICE_STATE_DISABLE = 2,
    AUDIO_DEVICE_STATE_NOTPRESENT = 4,
    AUDIO_DEVICE_STATE_UNPLUGGED = 8,
};

struct AUDIO_PEAK_INFO
{
    std::string        audioID = "";
    bool               isTrack = false;
    std::vector<float> peak{ .0f, .0f };         // [left, right]
    std::vector<float> devicePeak{ .0f, .0f };   // [left_dev, right_dev]
};

struct AudioPeakEvent
{
    std::vector<AUDIO_PEAK_INFO> infos;
};

struct AudioDefaultDeviceChangeEvent
{
    std::string       deviceID = "";
    AUDIO_DATA_FLOW   dataFlow = AUDIO_DATA_FLOW_INPUT;
    AUDIO_DEVICE_ROLE deviceRole = AUDIO_DEVICE_ROLE_CONSOLE;
    std::string       deviceName = "";
};

struct AudioDeviceStateChangeEvent
{
    std::string        deviceID = "";
    AUDIO_DEVICE_STATE deviceState = AUDIO_DEVICE_STATE_ALL;
};

struct AudioDeviceAddEvent
{
    std::string deviceID = "";
};

struct AudioDeviceRemoveEvent
{
    std::string deviceID = "";
};

struct AudioDevicePropertyChangeEvent
{
    std::string deviceID = "";
};


struct AudioCapableAppChangedEvent
{
};

struct PCMAudioEOFEvent
{
    std::string audioID = "";
};

struct PCMAudioBreakEvent
{
    std::string audioID = "";
    UINT32      leftSampleCnt = 0;
};

struct EchoDetectionResultEvent
{
    float probability = 0.0;
};

enum DEVICE_TRANSPORT_TYPE
{
    DEVICE_TRANSPORT_UNKNOWN = 0,
    DEVICE_TRANSPORT_BUILT_IN,
    DEVICE_TRANSPORT_BLUETOOTH,
    DEVICE_TRANSPORT_VIRTUAL,
    DEVICE_TRANSPORT_USB,
    DEVICE_TRANSPORT_DISPLAY_AUDIO,
    DEVICE_TRANSPORT_PCI,
    DEVICE_TRANSPORT_AIR_PLAY,
};

struct WASAPIDeviceInfoEvent
{
    std::string           deviceID = "";
    std::string           audioID = "";
    bool                  mic = false;
    bool                  speaker = false;
    bool                  systemMute = false;
    float                 systemCaptureVolume = .0f;
    DEVICE_TRANSPORT_TYPE type{};
};

struct WASAPIEnableRawDataModeFailedEvent
{
    std::string deviceID = "";
    std::string audioID = "";
    INT32       hresult = 0;
};

struct WASAPIGetMicrophoneBoostFailedEvent
{
    std::string deviceID = "";
    std::string audioID = "";
    INT32       hresult = 0;
};

struct AudioBufferMisalignmentEvent
{
    std::string audioID = "";
    UINT32      tooEarly = 0;
    UINT32      tooLate = 0;
};

struct AudioFailedStatusEvent
{
    std::string audioID = "";
    std::string errMsg = "";
    UINT32      errCode = 0;
    AUDIO_TYPE  audioType = AUDIO_NONE;
    INT32       hresult = 0;
};

struct EffectStateEvent
{
    std::string layerID = "";
    bool        valid = false;
};

struct EffectResourceLoadEvent
{
    std::string visualID = "";
    UINT64      msgID = 0;
    INT64       arg1 = 0;
    INT64       arg2 = 0;
    std::string arg3 = "";
    std::string effectFirstFrameElapsedTime = "";
    std::string effectPostFirstFrameElapsedTime = "";
};

struct EffectMsgEvent
{
	std::string visualID = "";
	UINT64      msgID = 0;
	INT64       arg1 = 0;
	INT64       arg2 = 0;
	std::string arg3 = "";
};

struct EffectPlatformEvent
{
    std::string serializedJson = "";
};

struct EffectGLVersionEvent
{
    UINT64 version = 1;
};

struct EffectLagEvent
{
    std::string visual_id;
    std::string effect_info;
    bool is_first_frame = false;
    UINT64 time;
    INT32 cost;
    INT32 type;
};

struct CommonMetricsEvent
{
    std::string              filterID = "";
    std::vector<std::string> mediaIDs{};
    std::string              commonMetrics = "";
};

struct ABRBitrateChangeEvent
{
    std::string streamID = "";
    UINT32      curBitrate = 0;
    UINT32      prevBitrate = 0;
};

enum STREAM_OUTPUT_ACTION
{
    OUTPUT_ACTION_UNDEFINED = 0,
    // RTMP
    OUTPUT_ACTION_CONNECTING,
    OUTPUT_ACTION_CONNECTED,
    OUTPUT_ACTION_DISCONNECTING,
    OUTPUT_ACTION_DISCONNECTED,
    OUTPUT_ACTION_RECONNECTING,
    OUTPUT_ACTION_ENCODE_FAILED,

    // RTC
    OUTPUT_ACTION_RTC_STARTED,
    OUTPUT_ACTION_RTC_STOPED,

    // NDI
    OUTPUT_ACTION_NDI_STARTED,
    OUTPUT_ACTION_NDI_STOPED,

    // VIRTUAL
    OUTPUT_ACTION_VIRTUAL_STARTED,
    OUTPUT_ACTION_VIRTUAL_STOPED,
    OUTPUT_ACTION_FIRST_FRAME,
    OUTPUT_ACTION_OPTIMIZE_IP,
    OUTPUT_ACTION_RTS_TRACKLOG,
    OUTPUT_ACTION_RTC_REOPEN,
    OUTPUT_ACTION_EVENT_TRACK,
    OUTPUT_ACTION_TRANSMIT_PULL_ERROR,
    OUTPUT_ACTION_TRANSMIT_NO_AUDIO_STREAM,
    OUTPUT_ACTION_TRANSMIT_NO_VIDEO_STREAM,
    OUTPUT_ACTION_TRANSMIT_AUDIO_VOLUME,
    OUTPUT_ACTION_RTC_RECONNECTED,
    OUTPUT_ACTION_RTC_LOST,
    OUTPUT_ACTION_FALLBACK,

    // RTC CONNECT STATE
    OUTPUT_ACTION_RTC_DISCONNECTED,
    OUTPUT_ACTION_RTC_CONNECTING,
    OUTPUT_ACTION_RTC_CONNECTED,
    OUTPUT_ACTION_RTC_RECONNECTING,
    OUTPUT_ACTION_FALLBACK_SUCCESS,
};

enum STREAM_OUTPUT_CODE
{
    OUTPUT_SUCCESS = 0,
    OUTPUT_BAD_PATH,
    OUTPUT_CONNECT_FAILED,
    OUTPUT_INVALID_STREAM,
    OUTPUT_DISCONNECTED,
    OUTPUT_INVALID_VIDEO_CODEC,
    OUTPUT_INVALID_PARAMETER,
    OUTPUT_INVALID_CALL,
    OUTPUT_UNSUPPORTED_FORMAT,
    OUTPUT_FAILED,
    OUTPUT_VIDEO_ENCODE_ERROR,
    OUTPUT_MANUAL_RECONNECTION,
    OUTPUT_CONNECT_RTMPS_FAILED,
    OUTPUT_CONNECT_RTMPS_FAILED_CERT_VERIFY,
    OUTPUT_CONNECT_DNS_ERROR,
    OUTPUT_RECONNECT_OVERFLOW,
    OUTPUT_INVALID_AUDIO_CODEC,
};

struct StreamEvent
{
    std::string          streamID = "";
    STREAM_OUTPUT_ACTION action{};
    STREAM_OUTPUT_CODE   code{};
    std::string          extra = ""; // extraInfo. could be json or plain string
    UINT64               timeEpoch = 0;
    STREAM_TYPE          curType = STREAM_NONE;
    STREAM_TYPE          fallbackType = STREAM_NONE;
};

struct StreamEncodeEvent
{
    std::string streamID = "";
    std::string jsonInfo = "";
};

struct BandwidthEvent
{
    std::string                        streamID = "";
    STREAM_TYPE                        streamType = STREAM_NONE;
    float                              averageTransportBitrate = .0f;
    float                              probRtt = 4;
    float                              probBandwidth = 5;
    UINT32                             totalSends = 6;
    UINT32                             totalDrops = 7;
    UINT32                             totalDuration = 8;
    UINT32                             totalSendDuration = 9;
    RTMP_UPLOAD_SPEED_TEST_RESULT      result = RTMP_UPLOAD_SPEED_TEST_RESULT_DEFAULT;
    RTMP_UPLOAD_SPEED_TEST_FAIL_REASON failedReason = RTMP_UPLOAD_SPEED_TEST_NO_FAIL;
};

struct JoinChannelEvent
{
    std::string channel = "";
    std::string uid = "";
    INT32       errorCode = 0;
    bool        firstTime = false;
};

struct LeaveChannelEvent
{
};

struct RemoteEnterEvent
{
    std::string uid = "";
    INT32       elapsed = 0;
};

enum REMOTE_LEAVE_REASON
{
    REMOTE_LEAVE_REASON_QUIT = 0,
    REMOTE_LEAVE_REASON_DROPPED,
    REMOTE_LEAVE_REASON_SWITCH_TO_INVISIBLE,
    REMOTE_LEAVE_REASON_KICKED_BY_ADMIN,
};

struct RemoteLeaveEvent
{
    std::string         uid = "";
    REMOTE_LEAVE_REASON elapsed = REMOTE_LEAVE_REASON_QUIT;
};

struct EngineStartEvent
{
    INT32 ret = 0;
    INT32 errorCode = 0;
};

struct EnginePublishEvent
{
    INT32 ret = 0;
    INT32 errorCode = 0;
};

struct EngineStopEvent
{
};

struct RoomMessageReceivedEvent
{
    std::string uid = "";
    std::string msg = "";
};

enum STREAM_MIXING_EVENT
{
    STREAM_MIXING_EVENT_BASE = 0,
    STREAM_MIXING_EVENT_START,
    STREAM_MIXING_EVENT_START_SUCCEED,
    STREAM_MIXING_EVENT_START_FAILED,
    STREAM_MIXING_EVENT_UPDATE,
    STREAM_MIXING_EVENT_UPDATE_SUCCEED,
    STREAM_MIXING_EVENT_UPDATE_FAILED,
    STREAM_MIXING_EVENT_STOP,
    STREAM_MIXING_EVENT_STOP_SUCCEED,
    STREAM_MIXING_EVENT_STOP_FAILED,
    STREAM_MIXING_EVENT_CHANGE_MIX_TYPE,
    STREAM_MIXING_EVENT_FIRST_AUDIO_FRAME_CLIENT_MIX,
    STREAM_MIXING_EVENT_FIRST_VIDEO_FRAME_CLIENT_MIX,
    STREAM_MIXING_EVENT_UPDATE_TIMEOUT,
    STREAM_MIXING_EVENT_START_TIMEOUT,
    STREAM_MIXING_EVENT_REQUEST_PARAM_ERROR,
    STREAM_MIXING_EVENT_MIX_IMAGE_EVENT,
    STREAM_MIXING_EVENT_MIX_SINGLE_WAY_CHORUS_EVENT,
    STREAM_MIXING_EVENT_MIX_STREAM_MIXING_MAX,
    STREAM_MIXING_EVENT_ALTERNATE_IMAGE_SUCCEED,
    STREAM_MIXING_EVENT_ALTERNATE_IMAGE_FAILED,
    STREAM_MIXING_EVENT_BACKGROUND_URL_SUCCEED,
    STREAM_MIXING_EVENT_BACKGROUND_URL_FAILED,
};

struct StreamMixingEvent
{
    std::string         taskID = "";
    STREAM_MIXING_EVENT eventType = STREAM_MIXING_EVENT_BASE;
    INT32               error = 0;
};

struct RemoteStreamAddEvent
{
    std::string uid = "";
    UINT32      streamIdx = 0;
    bool        isLocal = false;
    bool        isVideo = false;
};

struct RemoteStreamRemoveEvent
{
    std::string uid = "";
    UINT32      streamIdx = 0;
    bool        isLocal = false;
    bool        isVideo = false;
};

struct UserMessageReceivedEvent
{
    std::string uid = "";
    std::string msg = "";
};

struct RemoteVideoSizeChangeEvent
{
    std::string uid = "";
    UINT32      streamIdx = 0;
    UINT32      width = 0;
    UINT32      height = 0;
    UINT32      rotation = 0;
};

struct RemoteFirstVideoFrameDecodedEvent
{
    std::string uid = "";
    UINT32      streamIdx = 0;
    UINT32      width = 0;
    UINT32      height = 0;
    UINT32      rotation = 0;
};

struct RemoteFirstAudioFrameEvent
{
    std::string uid = "";
    UINT32      streamIdx = 0;
};

struct RemoteFirstVideoFrameRenderEvent
{
    std::string uid = "";
    UINT32      streamIdx = 0;
    UINT32      width = 0;
    UINT32      height = 0;
    UINT32      rotation = 0;
};

struct LocalAudioVolumeIndicationEvent
{
    std::string jsonContent = "";
};

struct RemoteAudioVolumeIndicationEvent
{
    std::string jsonContent = "";
};

struct StreamStatsEvent
{
    std::string uid = "";
    UINT32      streamIdx = 0;
    UINT32      videoSendTargetBitrate = 0;
    UINT32      videoSendBitrate = 0;
    UINT32      videoSentRate = 0;
    UINT32      videoLossRate = 0;
    UINT32      videoEncoderTargetRate = 0;
    UINT32      videoEncoderRate = 0;
    UINT32      localTxQuality = 0;
    UINT32      width = 0;
    UINT32      height = 0;
    UINT32      codecType = 0;
};

struct NETWORK_STATES_INFO
{
    std::string uid = "";
    float       fractionLost = .0f;
    UINT32      rtt = 0;
    UINT32      totalBandwidth = 0;
    UINT32      txQuality = 0;
    UINT32      rxQuality = 0;
};

struct NetworkStatsEvent
{
    std::vector<NETWORK_STATES_INFO> infos{};
};

struct ErrorEvent
{
    INT32 error = 0;
};

struct ConnectionStateEvent
{
    INT32 state = 0;
};

struct UserMessageSendResultEvent
{
    UINT64 messageID = 0;
    INT32  result = 0;
};

struct WarningEvent
{
    INT32 warning = 0;
};

struct ConnectionLostEvent
{
};

struct ActiveSpeakerEvent
{
    std::string uid = "";
};

struct FORWARD_STREAM_STATE_INFO
{
    std::string roomID = "";
    UINT32      forwardStateMask = 0;
    UINT32      forwardErrorMask = 0;
};

struct ForwardStreamStateInfoEvent
{
    std::vector<FORWARD_STREAM_STATE_INFO> infos{};
};

struct PerformanceEvent
{
    std::string name = "";
    UINT32      value = 0;
};

struct TeaEvent
{
    std::string name = "";
    std::string params = "";
};

enum DEVICE_LOST_REMOVE_REASON
{
    DEVICE_LOST_NO_ERROR = 0,
    DEVICE_LOST_DEVICE_REMOVED,
    DEVICE_LOST_DEVICE_RESET,
    DEVICE_LOST_DRIVER_INTERNAL_ERROR,
    DEVICE_LOST_DEVICE_HUNG,
    DEVICE_LOST_OUT_OF_MEMORY,
};

struct DevLostEvent
{
    std::string str = "";
    std::string driverDate = "";
    std::string driverName = "";
    std::string driverVer = "";
    std::string preDriverDate = "";
    std::string preDriverName = "";
    std::string preDriverVer = "";
    std::string removeReason = "";
    bool        effect = false;
    bool        displayChange = false;
    INT32       error = 0;
};

struct VqosDataReportEvent
{
    std::string data = "";
};

struct ColorPickerPixelEvent
{
    UINT32 color = 0;
};

enum THREAD_ID_INFO
{
    THREAD_ID_RENDER = 0,
};

enum THREAD_MONITOR_EVENT_TYPE
{
    THREAD_MONITOR_START = 0,
    THREAD_MONITOR_TICK,
    THREAD_MONITOR_END,
};

struct ThreadMonitorEvent
{
    THREAD_ID_INFO            threadID = THREAD_ID_RENDER;
    THREAD_MONITOR_EVENT_TYPE eventType = THREAD_MONITOR_START;
};

struct AudioSourceFormatEvent
{
    std::string audioID = "";
    UINT32      format = 0;
    UINT32      channel = 0;
    UINT32      sampleRate = 0;
    UINT32      layout = 0;
    UINT32      blockSize = 0;
};

struct AudioSourceErrorFormatEvent
{
    std::string audioID = "";
    UINT32      channel = 0;
    UINT32      sampleRate = 0;
    UINT32      bitsPerSample = 0;
    UINT32      channelMask = 0;
};

struct AudioSystemDeviceVolumeEvent
{
	std::string audioID = "";
	float system_capture_volume = .0f;
	bool system_mute = false;
};

enum PHONECAMERA_DEVICE_TYPE
{
    PHONECAMERA_DEVICE_TYPE_WIRELESS = 0,
    PHONECAMERA_DEVICE_TYPE_WIRED_ANDROID = 1,
    PHONECAMERA_DEVICE_TYPE_WIRED_IOS = 2,
};

struct PhoneCameraOnStartResultEvent
{
    INT32       err_code;
    std::string ip_list;
    INT32       port;
};

struct PhoneCameraOnConnectEvent
{
    INT32       err_code;
    PHONECAMERA_DEVICE_TYPE device_type;
    string                  deviceKey;
    string                  name;
};

struct PhoneCameraOnCameraReadyResultEvent
{
    INT32 err_code;
};

struct PhoneCameraOnClosedEvent
{
    ;
};

struct PhoneCameraOnDisconnectEvent
{
    INT32 err_code;
    string deviceKey;
    string msg;
};

enum PHONECAMERA_DEVICE_FOUND_OPT
{
    PHONECAMERA_DEVICE_FOUND_OPT_ADD,
    PHONECAMERA_DEVICE_FOUND_OPT_DEL,
};

struct PhoneCameraOnDeiviceFoundEvent
{
    INT32  err_code;
    PHONECAMERA_DEVICE_FOUND_OPT opt;
    PHONECAMERA_DEVICE_TYPE device_type;
    string                  deviceKey;
    string                  name;
};

struct PhoneCameraOnErrorFoundEvent
{
    INT32 err_code;
    string msg;
};

struct PhoneCameraOnRecvMetaDataFoundEvent
{
    string src;
    string msg;
};

struct VisualTextureReq
{
    std::string visual_id;
    int         targetWidth;
    int         targetHeight;
    int         clip_x;
    int         clip_y;
    int         clip_z;
    int         clip_w;
};

struct VisualTextureRsp
{
    int                        width;
    int                        height;
    int                        row_pitch;
    VIDEO_PIXEL_FORMAT         format;
    std::vector<unsigned char> data;
};

enum AUDIO_SOURCE_WARNING_TYPE
{
    NO_WARNING = 0,
    SILENT_FRAME = 1,
    NO_FRAME = 2,
    SILENT_FRAME_RECOVER = 3,
    NO_FRAME_RECOVER = 4,
};

struct AudioSourceWarningEvent
{
    std::string audioID = "";
    AUDIO_SOURCE_WARNING_TYPE  warningCode = NO_WARNING;
};

struct AudioLyraxRawDataResultEvent
{
    std::string audioID = "";
    int      raw_data_api_option = 0;
    int      raw_data_api_decision_option = 0;
    int      apply_raw_data_option = 0;
    int      system_state = 0;
    bool     apply_result = 0;
};

struct AudioDeviceCaptureRateCheckResultEvent
{
    std::string audioID = "";
    int32_t     raw_rate = 0;
    int32_t     diff_rate = 0;
};

class IIPCMgr
{
public:
    virtual void SendPBMessage(const std::string& message, const std::string& body) = 0;
};

struct EffectProfilerInfo
{
	int32_t before_effect_fps = 0;
	int32_t after_effect_fps = 0;
	float effect_achieve_rate = 0.f;
	float tick_achieve_rate = 0.f;
};

struct VideoQualityManagerGearResolution
{
	std::vector<int32_t> wide_screen;
	std::vector<int32_t> standard_screen;
};

enum VisualManagerTypeEnum
{
    kVisualManager_Default = 1,
    kVisualManager_GameSourceManager = 2,
    kVisualManager_CameraSourceManager = 3,
};

struct VideoQualityManagerGear
{
	int32_t                           id;
	std::string                       name;
	VideoQualityManagerGearResolution resolution;
	int32_t                           fps;
};

struct VideoQualityManagerBwGearIdEntry
{
	int64_t min;
	int64_t max;
	int32_t gear_id;
};

struct VideoQualityManagerVideoBitrate
{
	int32_t min;
	int32_t target;
	int32_t max;
};

struct VideoQualityManagerBitrate
{
	int32_t                                                audio;
	int32_t                                                gear_id;
	std::map<std::string, VideoQualityManagerVideoBitrate> video_wide_screen;
	std::map<std::string, VideoQualityManagerVideoBitrate> video_standard_screen;
};

struct VideoQualityManagerTopicConfig
{
	std::vector<VideoQualityManagerBwGearIdEntry> bw_table;
	std::map<std::string, int32_t>                device_level_table;
	int32_t                                       quality_perf_level_step;
	bool                                          enable_camera_enc_linkage;
	std::vector<VideoQualityManagerBitrate>       bitrate_table;
	std::vector<int32_t>                          topic_id_list;
	std::string                                   topic_type;
};

struct VideoQualityManagerStrategyConfig
{
	int32_t priority;
	uint32_t bitrate_table_index;
	int32_t default_level;
	std::map<int32_t, int32_t> gear_shift_rule;
	// 1:down,2:up,3:down_and_up
	int32_t gear_shift_type;
	std::string name;
};

struct VideoQualityManagerBwReserveFactorEntry
{
	int64_t min;
	int64_t max;
	float   reservation;
};

struct VideoQualityManagerDualCanvasConfig
{
	std::vector<VideoQualityManagerBwReserveFactorEntry> bw_probe_reservation;
	float                                                bitrate_alloc_ratio;
};

struct VideoQualityManagerRect
{
	int32_t width;
	int32_t height;
};

struct VideoQualityManagerCameraSuggestedConfig
{
	std::vector<int32_t> resolution;
	std::vector<int32_t> fps;
};

struct VideoQualityManagerCameraRecommendConfig
{
	// key: device level
	std::map<std::string, VideoQualityManagerCameraSuggestedConfig> device_level_camera_recommend_config;
	int32_t                                                         raise_threshold_for_fps;
};

struct VideoQualityManagerCameraDefaultRecommend
{
	VideoQualityManagerRect resolution;
	int32_t                 fps;
};

struct VideoQualityManagerRecommendStrategy
{
	std::vector<VideoQualityManagerTopicConfig>    topic_config;
	std::vector<VideoQualityManagerStrategyConfig> gear_table_priority;
	VideoQualityManagerDualCanvasConfig            dual_canvas_config;
	VideoQualityManagerCameraRecommendConfig       camera_recommend_config;
	std::string                                    default_bitrate_table_topic;

    // for v1
	std::optional<VideoQualityManagerCameraDefaultRecommend> camera_default_recommend;
};

struct VideoQualityManagerGoLiveParams
{
	int32_t width;
	int32_t height;
	int32_t fps;
};

struct VideoQualityManagerVideoResolutionOut
{
	float   aspect_ratio;
	int32_t width;
	int32_t height;
};

struct VideoQualityManagerGearOut
{
	int32_t                           id;
	std::string                       name;
	VideoQualityManagerGearResolution resolution;
	int32_t                           fps;
};

struct VideoQualityManagerBitrateOut
{
	int32_t audio;
	int32_t gear_id;
	// key:codec
	std::map<std::string, VideoQualityManagerVideoBitrate> video_wide_screen;
	std::map<std::string, VideoQualityManagerVideoBitrate> video_standard_screen;
	float                                                  dual_canvas_video_bitrate_alloc_ratio;
};

struct VideoQualityManagerGoLiveParamsOut
{
	VideoQualityManagerGearOut    gear;
	VideoQualityManagerBitrateOut bitrate;
	std::string                   decision_process_info;
};

struct VideoQualityManagerTopicGoLiveParams
{
	VideoQualityManagerGoLiveParamsOut quality_priority;
	VideoQualityManagerGoLiveParamsOut performance_priority;
};

struct VideoQualityManagerRecommendedGoLiveParams
{
	std::string          topic_type;
	std::vector<int32_t> topic_id_list;
	VideoQualityManagerTopicGoLiveParams topic_go_live_params;
};

struct VideoQualityManagerCameraStrategy
{
	std::vector<VIDEO_PIXEL_FORMAT> format_list;
	int32_t                           width;
	int32_t                           height;
	int32_t                           fps;
	int32_t                           min_fps;
	int32_t                           max_fps;
};

enum VideoQualityManagerQuailtyMode
{
	kVideoQualityManagerQuailtyMode_ModeUnknown = 0,
	kVideoQualityManagerQuailtyMode_QualityPriority,
	kVideoQualityManagerQuailtyMode_VQMPerfPriroity,
};

struct VideoQualityManagerCameraParams
{
	int32_t            width;
	int32_t            height;
	int32_t            fps;
	int32_t            max_fps;
	int32_t            min_fps;
    VIDEO_PIXEL_FORMAT format;
};

struct VideoQualityManagerCameraParamsOut
{
	int32_t            width;
	int32_t            height;
	int32_t            fps;
	int32_t            max_fps;
	int32_t            min_fps;
	VIDEO_PIXEL_FORMAT format;
	std::vector<int32_t> optional_fps;
};

struct VideoQualityManagerInitializeParam
{
	std::vector<VideoQualityManagerGear> gear_table;
	VideoQualityManagerRecommendStrategy recommend_strategy;
	std::map<std::string, std::string>   current_topic_device_level;
	bool                                 use_old_recommend_way;
};

struct VideoQualityManagerQueryCameraRecommendedParamsRequest
{
	std::string                                    camera_name;
	std::optional<std::string>                     current_device_level;
	std::vector<VideoQualityManagerCameraParams>   capture_params_list;
	std::optional<VideoQualityManagerGoLiveParams> current_enc_params;
	std::optional<VideoQualityManagerCameraParams> current_camera_params;
	std::optional<int32_t>                           topicId;
	std::optional<VideoQualityManagerCameraStrategy> strategy;
};

struct VideoQualityManagerQueryCameraRecommendedParamsResponse
{
	VideoQualityManagerCameraParamsOut              recommend_params;
	std::string                                     resolution_prompt;
	std::string                                     fps_prompt;
	std::vector<VideoQualityManagerCameraParamsOut> optional_params;
};

struct VideoQualityManagerQueryGoLiveRecommendedParamsRequest
{
	std::optional<int64_t>                         bw_probe_bps;
	std::map<std::string, std::string>             current_topic_device_level;
	std::optional<VideoQualityManagerCameraParams> camera_max_params;
};

struct VideoQualityManagerQueryManuallySelectedResultRequest
{
	int32_t gear_id;
	std::optional<int32_t> topic_id;
};

struct VideoQualityManagerQueryCameraBestParamsForTargetRequest
{
	VideoQualityManagerCameraStrategy            camera_strategy;
	std::vector<VideoQualityManagerCameraParams> capture_params_list;
	std::string                                  current_device_level;
};

struct VideoQualityManagerQueryCameraBestParamsForTargetResponse
{
	VideoQualityManagerCameraParamsOut camera_best_params;
};

struct VideoQualityManagerQueryManuallySelectedResultResponse
{
	VideoQualityManagerGoLiveParamsOut go_live_params;
};

struct VideoQualityManagerQueryGoLiveRecommendedParamsResponse
{
	std::vector<VideoQualityManagerRecommendedGoLiveParams> go_live_recommend_params;
};

struct VideoOutputParamsRequest
{
	std::string stream_id;
	int32_t width;
	int32_t height;
	uint32_t fps;
	std::string reason;
	std::string abr_strategy;
};

struct FallbackVideoEncoderParamsRequest
{
	string codec_id;
	string codec_name;
	int bitrate;
	string codec_param_json;
};

struct VideoOutputParamsResponse
{
	int32_t result;
};